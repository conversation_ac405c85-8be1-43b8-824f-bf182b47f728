// preload.js

const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  requestMicPermission: () => ipcRenderer.invoke('request-mic-permission'),
  captureScreen: () => ipcRenderer.invoke('capture-screen'),
  performOCR: async (imageBuffer) => {
    try {
      const user = JSON.parse(window.localStorage.getItem('helperCredentials') || '{}');
      if (!user || !user.email || !user.secretKey) {
        console.error('[Preload] Missing credentials in localStorage for OCR');
        return 'Missing credentials. Please sign in again.';
      }
      console.log('[Preload] Using credentials for OCR:', user.email);
      
      const response = await fetch(`http://localhost:8080/ocr?email=${encodeURIComponent(user.email)}`, {
        method: 'POST',
        headers: { 
          'Content-Type': 'image/png',
          'x-secret-key': user.secretKey
        },
        body: imageBuffer,
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[Preload] OCR request failed:', response.status, errorText);
        throw new Error(`OCR request failed: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      return result.text || 'OCR failed';
    } catch (error) {
      console.error('[Preload] OCR error:', error.message);
      return `OCR error: ${error.message}`;
    }
  },
  analyzeWithAI: async (text, conversation = [], model = 'gpt-4o-mini', language = null) => {
    try {
      const user = JSON.parse(window.localStorage.getItem('helperCredentials') || '{}');
      if (!user || !user.email || !user.secretKey) {
        console.error('[Preload] Missing credentials in localStorage for AI analysis');
        return 'Missing credentials. Please sign in again.';
      }
      console.log('[Preload] Using credentials for AI analysis:', user.email);
      console.log('[Preload] Conversation history for analysis:', conversation);
      console.log('[Preload] Model:', model, 'Language:', language);

      const response = await fetch(`http://localhost:8080/analyze`, {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'x-secret-key': user.secretKey
        },
        body: JSON.stringify({
          text,
          conversation,
          model,
          language,
          email: user.email
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[Preload] AI analysis request failed:', response.status, errorText);
        throw new Error(`AI analysis request failed: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      return result.response || 'AI analysis failed';
    } catch (error) {
      console.error('[Preload] AI analysis error:', error.message);
      return `AI analysis error: ${error.message}`;
    }
  },
  openExternalLink: (url) => ipcRenderer.send('open-external-link', url),
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  quitApp: () => ipcRenderer.send('quit-app'),
  onAuthCallback: (callback) => ipcRenderer.on('auth-callback', callback),
  onCapture: (callback) => ipcRenderer.on('trigger-capture', callback),
  onProcess: (callback) => ipcRenderer.on('trigger-process', callback),
  closeWindow: () => ipcRenderer.invoke('close-window'),
  moveWindow: (x, y) => ipcRenderer.invoke('move-window', x, y),
  resizeWindow: (width, height) => ipcRenderer.invoke('resize-window', width, height),
  getWindowPosition: () => ipcRenderer.invoke('get-window-position'),
  getWindowSize: () => ipcRenderer.invoke('get-window-size'),
  toggleWindow: () => ipcRenderer.invoke('toggle-window'),
  launchHelper: () => ipcRenderer.invoke('launch-helper'),
  saveToFile: (content) => ipcRenderer.invoke('save-to-file', content),
  // Audio Recording APIs
  getAudioSources: () => ipcRenderer.invoke('get-audio-sources'),
  startRecording: () => ipcRenderer.invoke('start-recording'),
  stopRecording: () => ipcRenderer.invoke('stop-recording'),
  onAudioChunk: (callback) => ipcRenderer.on('audio-chunk', (event, data) => callback(data)),
  onRecordingStarted: (callback) => ipcRenderer.on('recording-started', callback),
  onRecordingStopped: (callback) => ipcRenderer.on('recording-stopped', callback),
  // Generic IPC event listener
  on: (channel, callback) => ipcRenderer.on(channel, (event, ...args) => callback(event, ...args)),
});

console.log('[Preload] Preload script completed initialization');