<!DOCTYPE html>
<html>
<head>
  <title>Screen Capture Tool</title>
  <link rel="stylesheet" href="styles.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/vs2015.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
  <!-- Add Mermaid.js for diagram rendering -->
  <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
</head>
<body>
  <!-- Loading Overlay -->
  <div class="loading-overlay" id="loadingOverlay">
    <i class="fas fa-spinner fa-spin"></i>
    <div class="loading-message">Processing...</div>
    <div class="sub-message">Please wait a moment.</div>
  </div>

  <div class="title-bar">
    <h3>interviewcracker Helper</h3>
    <button class="close-btn" id="closeBtn">×</button>
  </div>
  <div class="container">
    <div class="shortcuts" id="shortcuts-header">
      <!-- Populated dynamically -->
    </div>
    
    <div class="tabs">
      <div class="tab active" data-tab="assistant">Assistant</div>
      <div class="tab" data-tab="system-design">System Design</div>
      <div class="tab" data-tab="audio">Audio</div>
      <div class="tab" data-tab="shortcuts">Shortcuts</div>
    </div>
    
    <div class="main-content">
      <div class="tab-content active" id="assistant-tab">
        <div class="chat-section">
          <div class="chat-header">
            <span>AI Chat</span>
            <span id="chat-status">Ready</span>
          </div>
          <div class="chat-messages" id="chat-messages">
            <div class="message ai-message">
              Hello! I’m here to help. You can chat with me or capture a screenshot (Ctrl+Shift+C, then Ctrl+Shift+P to process). Select a model to get started!
              <div class="message-time">Now</div>
            </div>
          </div>
          <div class="chat-input-container">
            <select id="language-select" class="language-select">
              <option value="Python">Python</option>
              <option value="JavaScript">JavaScript</option>
              <option value="Java">Java</option>
              <option value="C++">C++</option>
            </select>
            <select id="model-select" class="model-select">
              <!-- <option value="gpt-4">GPT-4</option>
              <option value="gpt-4o">GPT-4o</option>
              <option value="o1-preview">o1-preview</option> -->
            </select>
            <input type="text" class="chat-input" id="chat-input" placeholder="Type your message..." />
            <button class="send-button" id="send-button">Send</button>
            <button class="clear-chat" id="clear-chat">Clear</button>
          </div>
        </div>
      </div>
      
      <div class="tab-content" id="system-design-tab">
        <div class="chat-section">
          <div class="chat-header">
            <span>System Design Chat</span>
            <span id="system-design-status">Ready</span>
          </div>
          <div class="chat-messages" id="system-design-messages">
            <div class="message ai-message">
              Hello! I can help with system design. Describe your problem (e.g., "Design a scalable e-commerce system") or capture a screenshot (Ctrl+Shift+C, then Ctrl+Shift+P to process). I’ll provide a diagram and detailed explanation.
              <div class="message-time">Now</div>
            </div>
          </div>
          <div class="chat-input-container">
            <select id="system-design-model-select" class="model-select">
            </select>
            <input type="text" class="chat-input" id="system-design-input" placeholder="Describe your system design problem..." />
            <button class="send-button" id="system-design-send-button">Send</button>
            <button class="clear-chat" id="system-design-clear-chat">Clear</button>
          </div>
        </div>
      </div>
      
      <div class="tab-content" id="audio-tab">
        <div class="audio-container">
          <div class="recording-header">
            <span>Audio Recording</span>
            <span id="recording-status">Ready</span>
          </div>
          <div class="recording-controls">
            <button id="startRecordingBtn" class="control-btn" title="Start Recording (Ctrl+Shift+R)">
              <i class="fas fa-microphone"></i> Start Recording
            </button>
            <button id="stopRecordingBtn" class="control-btn" title="Stop Recording (Ctrl+Shift+R)" disabled>
              <i class="fas fa-stop"></i> Stop Recording
            </button>
          </div>
          <div class="recording-info" id="recording-info">
            Recordings will be saved to audio/recording folder
          </div>
        </div>
      </div>
      
      <div class="tab-content" id="shortcuts-tab"></div>
      
      <div class="status-bar">
        <span id="status">Ready</span>
        <span id="window-size"></span>
      </div>
    </div>
  </div>

  
  <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/4.0.2/marked.min.js"></script>
  <script src="ui.js"></script>
  <script src="app.js" defer></script>
</body>
</html>