:root {
  --primary-color: #e11d48;
  --secondary-color: #be123c;
  --accent-color: #fb7185;
  --background-color: #0f172a;
  --card-bg-color: #1e293b;
  --card-secondary-bg: #334155;
  --text-color: #e2e8f0;
  --text-secondary: #94a3b8;
  --border-color: #334155;
  --success-color: #059669;
  --border-radius: 8px;
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --header-height: 73px;
  --footer-height: 49px;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.5;
  min-height: 100vh;
}

.layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Header Styles */
.header {
  background-color: #111827;
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: var(--shadow);
  position: sticky;
  top: 0;
  z-index: 100;
  height: var(--header-height);
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  background-color: #374151;
  margin-right: 12px;
}

.logo-text {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-color);
  letter-spacing: -0.01em;
}

/* Layout Structure */
.content-wrapper {
  display: flex;
  flex: 1;
  overflow: visible; /* Changed from hidden to fix cutoff */
  padding-top: 0; /* Removed padding to fix spacing */
  position: relative; /* Add this to contain the fixed sidebar */
  min-height: calc(100vh - var(--header-height) - var(--footer-height));
}

/* Shortcuts Sidebar */
.shortcuts-sidebar {
  background-color: var(--card-bg-color);
  border-right: 1px solid var(--border-color);
  width: 280px;
  padding: 20px;
  overflow-y: auto;
  box-shadow: var(--shadow);
  display: flex;
  flex-direction: column;
  gap: 16px;
  position: fixed;
  left: 0;
  top: var(--header-height);
  bottom: var(--footer-height); /* Fix to prevent cutting off at bottom */
  height: auto; /* Let it size naturally between top and bottom */
  z-index: 90;
  overflow: hidden; 
}

.shortcuts-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 16px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}

.shortcut-category {
  font-size: 15px;
  font-weight: 600;
  color: var(--accent-color);
  margin-top: 8px;
  margin-bottom: 12px;
  padding-left: 2px;
}

.shortcut-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  border-radius: 6px;
  margin-bottom: 8px;
  background-color: var(--card-secondary-bg);
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.shortcut-item:hover {
  background-color: #3b4863;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.shortcut-key {
  font-size: 13px;
  font-weight: 600;
  color: var(--primary-color);
  background-color: rgba(225, 29, 72, 0.15);
  padding: 4px 8px;
  border-radius: 4px;
  text-align: center;
  min-width: 90px;
  letter-spacing: 0.02em;
}

.shortcut-description {
  font-size: 14px;
  color: var(--text-color);
  padding: 4px 8px;

  text-align: center;
  min-width: 90px;
}

.Note {
  font-size: 12px;
  color: gray;
  padding: 4px 8px;
  text-align: center;
  min-width: 90px;
  margin-bottom: 10px;
}

/* Main Content */
.main-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32px 24px;
  overflow-y: auto;
  margin-left: 280px; /* Make space for the fixed sidebar */
  min-height: calc(100vh - var(--header-height) - var(--footer-height));
  width: calc(100% - 280px); /* Ensure it takes proper width */
}

.container {
  width: 100%;
  max-width: 480px;
  background: var(--card-bg-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  overflow: hidden;
  border: 1px solid var(--border-color);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  margin: 0 auto; /* Center the container */
}

.container:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 12px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
}

.card-header {
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color);
  background-color: #1a1a2e;
}

.card-header h2 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
  letter-spacing: -0.01em;
}

.welcome-message {
  font-size: 14px;
  color: var(--text-secondary);
  margin-top: 4px;
}

.card-body {
  padding: 24px;
}

/* Form Elements */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
}

input[type="text"],
input[type="password"] {
  width: 100%;
  padding: 12px 14px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: #334155;
  color: var(--text-color);
  font-size: 15px;
  transition: all 0.2s ease;
}

input[type="text"]:focus,
input[type="password"]:focus {
  border-color: var(--accent-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(225, 29, 72, 0.25);
}

input[type="text"]::placeholder,
input[type="password"]::placeholder {
  color: var(--text-secondary);
  opacity: 0.6;
}

.form-error {
  color: var(--primary-color);
  font-size: 14px;
  margin-top: 8px;
  display: flex;
  align-items: center;
}

.form-error:before {
  content: '⚠️';
  margin-right: 6px;
  font-size: 12px;
}

/* Buttons */
button {
  width: 100%;
  padding: 12px 16px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  letter-spacing: 0.02em;
}

button:hover {
  background: var(--secondary-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

button:active {
  transform: translateY(1px);
  box-shadow: none;
}

button:disabled {
  background-color: #475569;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.action-button {
  background-color: var(--success-color);
  margin-bottom: 12px;
}

.action-button:hover {
  background-color: #047857;
}

.secondary-button {
  background-color: var(--card-secondary-bg);
  border: 1px solid var(--border-color);
}

.secondary-button:hover {
  background-color: #475569;
}

/* Information Cards */
.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.info-card {
  background-color: var(--card-secondary-bg);
  border-radius: 8px;
  padding: 16px;
  border: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.info-header {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.info-value,
.max-prompts,
.prompts-used {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-color);
}

.plan-badge {
  display: inline-block;
  background-color: rgba(225, 29, 72, 0.15);
  color: var(--accent-color);
  font-size: 12px;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 9999px;
  margin-top: 4px;
}

.plan-badge.premium {
  background-color: rgba(249, 115, 22, 0.15);
  color: #f97316;
}

/* Timer Section */
.timer-section {
  background-color: var(--card-secondary-bg);
  border-radius: 8px;
  padding: 18px;
  margin-bottom: 24px;
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.timer-section:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  border-color: #475569;
}

.timer-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 14px;
}

.timer-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px dashed rgba(255, 255, 255, 0.1);
}

.timer-display:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.timer-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.timer-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
  background: rgba(255, 255, 255, 0.06);
  padding: 3px 8px;
  border-radius: 4px;
}

/* Messages */
.limit-message {
  background-color: rgba(225, 29, 72, 0.15);
  border: 1px solid var(--primary-color);
  color: var(--accent-color);
  padding: 14px;
  border-radius: 6px;
  font-size: 14px;
  text-align: center;
  margin-bottom: 16px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(225, 29, 72, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(225, 29, 72, 0); }
  100% { box-shadow: 0 0 0 0 rgba(225, 29, 72, 0); }
}

/* Auth Elements */
.auth-divider {
  display: flex;
  align-items: center;
  margin: 20px 0;
  color: var(--text-secondary);
  font-size: 14px;
}

.auth-divider::before,
.auth-divider::after {
  content: "";
  flex: 1;
  border-bottom: 1px solid var(--border-color);
}

.auth-divider::before {
  margin-right: 10px;
}

.auth-divider::after {
  margin-left: 10px;
}

.signup-link {
  margin-top: 16px;
  text-align: center;
  font-size: 14px;
  color: var(--text-secondary);
}

.signup-link a {
  color: var(--accent-color);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s;
}

.signup-link a:hover {
  text-decoration: underline;
  color: #ff8495;
}

/* Utilities */
.hidden {
  display: none !important;
}

/* Footer */
.footer {
  text-align: center;
  padding: 16px;
  font-size: 12px;
  color: var(--text-secondary);
  background-color: #111827;
  border-top: 1px solid var(--border-color);
  height: var(--footer-height);
  position: relative;
  z-index: 100;
}

/* Responsive Design */
@media (max-width: 992px) {
  .content-wrapper {
    flex-direction: column;
    overflow-x: hidden;
  }
  
  .shortcuts-sidebar {
    position: static; /* Remove fixed positioning on smaller screens */
    width: 100%;
    height: auto;
    max-height: 300px;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
    padding: 16px;
    position: relative;
    top: 0;
    bottom: auto;
  }
  
  .main-content {
    margin-left: 0; /* Remove margin on smaller screens */
    padding-top: 20px;
    width: 100%;
  }
  
  .shortcuts-title {
    margin-bottom: 12px;
  }
  
  .shortcut-item {
    padding: 8px 10px;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 12px 16px;
  }
  
  .main-content {
    padding: 16px 12px;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .container {
    max-width: 100%;
  }
  
  .card-header,
  .card-body {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .logo-text {
    font-size: 16px;
  }
  
  .logo {
    width: 32px;
    height: 32px;
    margin-right: 8px;
  }
  
  .shortcut-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .shortcut-key {
    width: 100%;
  }
  
  .timer-display {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .timer-value {
    align-self: flex-end;
  }
}