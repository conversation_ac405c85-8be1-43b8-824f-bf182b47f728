import soundcard as sc
import soundfile as sf
import sys
import time
import signal
import threading
import os
import numpy as np

# Configuration
OUTPUT_FILE_NAME = "out.wav"
SAMPLE_RATE = 44100
CHUNK_SIZE = 1024

# Flags to control recording
recording = False
should_exit = False

def signal_handler(sig, frame):
    global should_exit
    should_exit = True
    print("Signal received, preparing to exit")
    sys.stdout.flush()

signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

def record_audio():
    global recording, should_exit
    
    try:
        print("Initializing recorders...")
        sys.stdout.flush()
        
        # Initialize microphone recorder
        mic_recorder = sc.default_microphone().recorder(samplerate=SAMPLE_RATE)
        # Initialize loopback recorder
        try:
            loopback_recorder = sc.get_microphone(id=str(sc.default_speaker().name), include_loopback=True).recorder(samplerate=SAMPLE_RATE)
        except Exception as e:
            print(f"Loopback error: {e}. Proceeding with microphone only.")
            sys.stdout.flush()
            loopback_recorder = None
        
        with mic_recorder, (loopback_recorder if loopback_recorder else mic_recorder):
            print("Recorders ready")
            sys.stdout.flush()
            
            # Buffer for mixed audio
            mixed_buffer = []
            
            while not should_exit:
                if recording:
                    # Record both sources simultaneously
                    mic_chunk = mic_recorder.record(numframes=CHUNK_SIZE)
                    loopback_chunk = loopback_recorder.record(numframes=CHUNK_SIZE) if loopback_recorder else np.zeros_like(mic_chunk)
                    
                    # Mix the chunks immediately
                    try:
                        # Ensure chunks are the same length and mix
                        min_length = min(len(mic_chunk), len(loopback_chunk))
                        mixed_chunk = (mic_chunk[:min_length, 0] + loopback_chunk[:min_length, 0]) / 2
                        mixed_buffer.append(mixed_chunk)
                    except Exception as mix_error:
                        print(f"Error mixing audio: {mix_error}")
                        sys.stdout.flush()
                        mixed_buffer.append(mic_chunk[:, 0])  # Fallback to mic only
                
                else:
                    # Save when recording stops and there’s data
                    if mixed_buffer and len(mixed_buffer) > 0:
                        print(f"Saving recording to {OUTPUT_FILE_NAME}")
                        sys.stdout.flush()
                        try:
                            combined_data = np.concatenate(mixed_buffer, axis=0)
                            sf.write(file=OUTPUT_FILE_NAME, data=combined_data, samplerate=SAMPLE_RATE)
                            print(f"Recording saved to {OUTPUT_FILE_NAME}")
                            sys.stdout.flush()
                            mixed_buffer = []  # Clear buffer after saving
                        except Exception as save_error:
                            print(f"Error saving audio: {save_error}")
                            sys.stdout.flush()
                
                time.sleep(0.001)  # Minimal sleep for responsiveness
            
            # Final save on exit
            if mixed_buffer and len(mixed_buffer) > 0:
                print(f"Final save to {OUTPUT_FILE_NAME}")
                sys.stdout.flush()
                try:
                    combined_data = np.concatenate(mixed_buffer, axis=0)
                    sf.write(file=OUTPUT_FILE_NAME, data=combined_data, samplerate=SAMPLE_RATE)
                    print(f"Recording saved to {OUTPUT_FILE_NAME}")
                    sys.stdout.flush()
                except Exception as save_error:
                    print(f"Error saving audio on exit: {save_error}")
                    sys.stdout.flush()
                
    except Exception as e:
        print(f"Error in recording: {str(e)}")
        sys.stdout.flush()
        return False
    
    return True

def process_commands():
    global recording, should_exit
    
    if sys.platform == 'win32':
        import msvcrt
        msvcrt.setmode(sys.stdin.fileno(), os.O_BINARY)
    
    print("Command processor ready. Send 'y' to start, 'n' to stop, 'q' to quit.")
    sys.stdout.flush()
    
    while not should_exit:
        if sys.stdin.isatty():
            command = input().strip().lower()
        else:
            command = sys.stdin.readline().strip().lower()
            
        if not command:
            time.sleep(0.1)
            continue
            
        print(f"Command received: {command}")
        sys.stdout.flush()
        
        if command == 'y':
            if not recording:
                recording = True
                print("Recording started")
            else:
                print("Already recording")
        elif command == 'n':
            if recording:
                recording = False
                print("Recording stopped")
            else:
                print("Not currently recording")
        elif command == 'q':
            should_exit = True
            print("Exiting")
        
        sys.stdout.flush()

if __name__ == "__main__":
    print("Audio recorder starting...")
    sys.stdout.flush()
    
    record_thread = threading.Thread(target=record_audio)
    record_thread.daemon = True
    record_thread.start()
    
    command_thread = threading.Thread(target=process_commands)
    command_thread.daemon = True
    command_thread.start()
    
    try:
        while not should_exit and (record_thread.is_alive() or command_thread.is_alive()):
            time.sleep(0.1)
    except KeyboardInterrupt:
        print("Interrupted by user")
        should_exit = True
    finally:
        recording = False
        should_exit = True
        print("Waiting for threads to finish...")
        sys.stdout.flush()
        
        record_thread.join(timeout=2)
        command_thread.join(timeout=2)
        
        print("Recorder exited")
        sys.stdout.flush()