@echo off
echo Building Docker image...
docker build -t gcr.io/basic-curve-453309-a9/server .

@REM echo Authenticating Docker with Google Cloud...
@REM gcloud auth configure-docker

echo Pushing Docker image to Google Container Registry...
docker push gcr.io/basic-curve-453309-a9/server

echo Deploying to Cloud Run...
gcloud run deploy server ^
  --image gcr.io/basic-curve-453309-a9/server ^
  --platform managed ^
  --region us-central1 ^
  --allow-unauthenticated ^
  --port 8080 ^
  --min-instances 1

echo Deployment completed!
pause
