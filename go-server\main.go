package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"strconv"
	"strings"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"github.com/otiai10/gosseract/v2"
	"github.com/sashabaranov/go-openai"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
	"golang.org/x/time/rate"
)

// User represents user data structure
type User struct {
	Name               string     `bson:"name,omitempty" json:"name,omitempty"`
	Email              string     `bson:"email" json:"email"`
	SecretKey          string     `bson:"secretKey" json:"secretKey"`
	SubscriptionType   string     `bson:"subscriptionType" json:"subscriptionType"`
	SubscriptionEndDate *time.Time `bson:"subscriptionEndDate" json:"subscriptionEndDate"`
	RemainingTries     int        `bson:"remainingTries" json:"remainingTries"`
	PromptsUsed        int        `bson:"promptsUsed" json:"promptsUsed"`
	CreatedAt          time.Time  `bson:"createdAt" json:"createdAt"`
	LastResetDate      time.Time  `bson:"lastResetDate" json:"lastResetDate"`
}

// OCRResponse represents OCR API response
type OCRResponse struct {
	Text string `json:"text"`
}

// AnalyzeRequest represents the analyze API request
type AnalyzeRequest struct {
	Text     string `json:"text"`
	Email    string `json:"email"`
	SecretKey string `json:"secretKey"`
}

// ChatRequest represents the chat API request
type ChatRequest struct {
	Prompt    string `json:"prompt"`
	Email     string `json:"email"`
	SecretKey string `json:"secretKey"`
}

// ErrorResponse represents error API response
type ErrorResponse struct {
	Error string `json:"error"`
}

// Config holds application configuration
type Config struct {
	Port             string
	MongoURI         string
	OpenAIKey        string
	LatestVersion    string
	WorkerPoolSize   int
	RateLimitReqs    int
	RateLimitWindow  time.Duration
}

// App holds the application state
type App struct {
	Config       Config
	Router       *gin.Engine
	DB           *mongo.Client
	UserColl     *mongo.Collection
	OpenAIClient *openai.Client
	OCRPool      chan *gosseract.Client
}

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found")
	}

	// Initialize configuration
	config := Config{
		Port:            os.Getenv("PORT"),
		MongoURI:        os.Getenv("MONGODB_URI"),
		OpenAIKey:       os.Getenv("OPENAI_API_KEY"),
		LatestVersion:   os.Getenv("LATEST_VERSION"),
		WorkerPoolSize:  2,
		RateLimitReqs:   100,
		RateLimitWindow: 15 * time.Minute,
	}

	if config.Port == "" {
		config.Port = "8080"
	}

	if config.LatestVersion == "" {
		config.LatestVersion = "1.0.0"
	}

	// Initialize the application
	app := NewApp(config)
	defer app.Cleanup()

	// Start the server
	log.Printf("Server running at http://localhost:%s", config.Port)
	if err := app.Router.Run(":" + config.Port); err != nil {
		log.Fatalf("Server failed to start: %v", err)
	}
}

// NewApp initializes the application
func NewApp(config Config) *App {
	app := &App{
		Config:       config,
		Router:       gin.Default(),
		OpenAIClient: openai.NewClient(config.OpenAIKey),
		OCRPool:      make(chan *gosseract.Client, config.WorkerPoolSize),
	}

	// Initialize MongoDB connection
	app.initMongoDB()

	// Initialize OCR workers
	app.initOCRWorkers()

	// Set up routes
	app.setupRoutes()

	return app
}

// Cleanup performs necessary cleanup operations
func (app *App) Cleanup() {
	// Close MongoDB connection
	if app.DB != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		if err := app.DB.Disconnect(ctx); err != nil {
			log.Printf("Error disconnecting from MongoDB: %v", err)
		}
	}

	// Close OCR clients
	close(app.OCRPool)
	for client := range app.OCRPool {
		client.Close()
	}
}

// initMongoDB initializes the MongoDB connection
func (app *App) initMongoDB() {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	clientOptions := options.Client().
		ApplyURI(app.Config.MongoURI).
		SetServerSelectionTimeout(120 * time.Second).
		SetSocketTimeout(150 * time.Second).
		SetConnectTimeout(120 * time.Second).
		SetMaxPoolSize(10).
		SetMinPoolSize(3).
		SetRetryWrites(true).
		SetRetryReads(true)

	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		log.Fatalf("Failed to connect to MongoDB: %v", err)
	}

	// Ping the database
	if err = client.Ping(ctx, readpref.Primary()); err != nil {
		log.Fatalf("Failed to ping MongoDB: %v", err)
	}

	app.DB = client
	app.UserColl = client.Database("app").Collection("users")

	// Perform migration for users without lastResetDate
	filter := bson.M{"lastResetDate": bson.M{"$exists": false}}
	update := bson.M{"$set": bson.M{"lastResetDate": time.Now()}}
	result, err := app.UserColl.UpdateMany(ctx, filter, update)
	if err != nil {
		log.Printf("Migration error: %v", err)
	} else {
		log.Printf("Migration: Updated %d user documents with lastResetDate", result.ModifiedCount)
	}
}

// initOCRWorkers initializes the OCR worker pool
func (app *App) initOCRWorkers() {
	for i := 0; i < app.Config.WorkerPoolSize; i++ {
		client := gosseract.NewClient()
		if err := client.SetLanguage("eng"); err != nil {
			log.Printf("Error initializing OCR worker %d: %v", i+1, err)
			continue
		}
		app.OCRPool <- client
		log.Printf("Initialized worker %d", i+1)
	}
}

// setupRoutes sets up the API routes
func (app *App) setupRoutes() {
	// Set up rate limiting middleware
	limiter := rate.NewLimiter(rate.Every(app.Config.RateLimitWindow/time.Duration(app.Config.RateLimitReqs)), app.Config.RateLimitReqs)
	app.Router.Use(func(c *gin.Context) {
		if !limiter.Allow() {
			c.JSON(http.StatusTooManyRequests, ErrorResponse{Error: "Too many requests from this IP, please try again later."})
			c.Abort()
			return
		}
		c.Next()
	})

	// Routes
	app.Router.POST("/register", app.handleRegister)
	app.Router.POST("/verify-credentials", app.handleVerifyCredentials)
	app.Router.GET("/user-details", app.handleUserDetails)
	app.Router.POST("/ocr", app.handleOCR)
	app.Router.POST("/analyze", app.handleAnalyze)
	app.Router.POST("/audio", app.handleAudio)
	app.Router.POST("/chat", app.handleChat)
	app.Router.GET("/user-stats/:email", app.handleUserStats)
	app.Router.GET("/healthcheck", app.handleHealthcheck)
	app.Router.GET("/check-update", app.handleCheckUpdate)
}

// verifyUser middleware checks if the user is valid
func (app *App) verifyUser(c *gin.Context) (*User, bool) {
	email := c.Query("email")
	if email == "" {
		if c.Request.Method == http.MethodPost {
			var data map[string]string
			if err := c.ShouldBindJSON(&data); err == nil {
				email = data["email"]
			}
		}
	}

	secretKey := c.Query("secretKey")
	if secretKey == "" {
		if c.Request.Method == http.MethodPost {
			var data map[string]string
			if err := c.ShouldBindJSON(&data); err == nil {
				secretKey = data["secretKey"]
			}
		}
		if secretKey == "" {
			secretKey = c.GetHeader("x-secret-key")
		}
	}

	if email == "" || secretKey == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Email and secret key are required"})
		return nil, false
	}

	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	var user User
	filter := bson.M{"email": email}
	err := app.UserColl.FindOne(ctx, filter).Decode(&user)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "User not found"})
		} else {
			log.Printf("User verification error: %v", err)
			c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "Internal server error during user verification"})
		}
		return nil, false
	}

	if user.SecretKey != secretKey {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "Invalid secret key"})
		return nil, false
	}

	// Update subscription status
	updatedUser, err := app.updateSubscriptionStatus(&user)
	if err != nil {
		log.Printf("Error updating subscription status: %v", err)
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "Internal server error"})
		return nil, false
	}

	// Reset prompts if needed
	updatedUser, err = app.resetPromptsIfNeeded(updatedUser)
	if err != nil {
		log.Printf("Error resetting prompts: %v", err)
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "Internal server error"})
		return nil, false
	}

	return updatedUser, true
}

// verifyUserAndPromptLimit middleware checks user and prompt limits
func (app *App) verifyUserAndPromptLimit(c *gin.Context) (*User, int, bool) {
	user, ok := app.verifyUser(c)
	if !ok {
		return nil, 0, false
	}

	maxPrompts := 5
	if user.SubscriptionType == "premium" {
		maxPrompts = 250
	}

	if user.PromptsUsed >= maxPrompts {
		c.JSON(http.StatusForbidden, ErrorResponse{
			Error: fmt.Sprintf("Maximum number of prompts (%d) reached for %s subscription.",
				maxPrompts,
				strings.Title(user.SubscriptionType)),
		})
		return nil, 0, false
	}

	return user, maxPrompts, true
}

// handleRegister handles user registration
func (app *App) handleRegister(c *gin.Context) {
	var input struct {
		Name      string `json:"name" binding:"required"`
		Email     string `json:"email" binding:"required,email"`
		SecretKey string `json:"secretKey" binding:"required"`
	}

	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Invalid input"})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	// Check if user already exists
	count, err := app.UserColl.CountDocuments(ctx, bson.M{"email": input.Email})
	if err != nil {
		log.Printf("Error checking existing user: %v", err)
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "Registration failed"})
		return
	}

	if count > 0 {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Email already exists"})
		return
	}

	// Create new user
	now := time.Now()
	user := User{
		Name:             input.Name,
		Email:            input.Email,
		SecretKey:        input.SecretKey,
		SubscriptionType: "free",
		RemainingTries:   3,
		PromptsUsed:      0,
		CreatedAt:        now,
		LastResetDate:    now,
	}

	_, err = app.UserColl.InsertOne(ctx, user)
	if err != nil {
		log.Printf("Error inserting user: %v", err)
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "Registration failed"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"user": user,
	})
}

// handleVerifyCredentials verifies user credentials
func (app *App) handleVerifyCredentials(c *gin.Context) {
	var input struct {
		Email     string `json:"email" binding:"required,email"`
		SecretKey string `json:"secretKey" binding:"required"`
	}

	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Invalid input"})
		return
	}

	maxRetries := 3
	for retryCount := 0; retryCount < maxRetries; retryCount++ {
		ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)

		var user User
		err := app.UserColl.FindOne(ctx, bson.M{"email": input.Email}).Decode(&user)
		if err != nil {
			cancel()
			if err == mongo.ErrNoDocuments {
				c.JSON(http.StatusBadRequest, ErrorResponse{Error: "User not found"})
				return
			}

			log.Printf("Verification attempt %d failed: %v", retryCount+1, err)
			if retryCount < maxRetries-1 {
				time.Sleep(time.Duration(retryCount+1) * time.Second)
				continue
			}

			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error:   "Authentication failed",
				Details: "Database connection issue. Please try again in a moment.",
			})
			return
		}

		if user.SecretKey != input.SecretKey {
			cancel()
			c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "Invalid secret key"})
			return
		}

		// Update subscription status
		updatedUser, err := app.updateSubscriptionStatus(&user)
		if err != nil {
			cancel()
			log.Printf("Error updating subscription: %v", err)
			c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "Authentication failed"})
			return
		}

		// Reset prompts if needed
		updatedUser, err = app.resetPromptsIfNeeded(updatedUser)
		if err != nil {
			cancel()
			log.Printf("Error resetting prompts: %v", err)
			c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "Authentication failed"})
			return
		}

		cancel()
		c.JSON(http.StatusOK, gin.H{"user": updatedUser})
		return
	}
}

// handleUserDetails gets user details
func (app *App) handleUserDetails(c *gin.Context) {
	user, ok := app.verifyUser(c)
	if !ok {
		return
	}

	maxPrompts := 5
	if user.SubscriptionType == "premium" {
		maxPrompts = 250
	}

	c.JSON(http.StatusOK, gin.H{
		"user": gin.H{
			"name":               user.Name,
			"email":              user.Email,
			"secretKey":          user.SecretKey,
			"subscriptionType":   user.SubscriptionType,
			"subscriptionEndDate": user.SubscriptionEndDate,
			"remainingTries":     user.RemainingTries,
			"promptsUsed":        user.PromptsUsed,
			"lastResetDate":      user.LastResetDate,
			"maxPrompts":         maxPrompts,
		},
	})
}

// handleOCR processes OCR requests
func (app *App) handleOCR(c *gin.Context) {
	user, _, ok := app.verifyUserAndPromptLimit(c)
	if !ok {
		return
	}

	// Create uploads directory if it doesn't exist
	uploadDir := "uploads"
	if _, err := os.Stat(uploadDir); os.IsNotExist(err) {
		if err := os.MkdirAll(uploadDir, 0755); err != nil {
			log.Printf("Error creating upload directory: %v", err)
			c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "OCR processing failed"})
			return
		}
	}

	// Process uploaded file or raw body
	var filePath string
	file, header, err := c.Request.FormFile("image")
	if err == nil {
		// Handle multipart form upload
		filename := filepath.Join(uploadDir, fmt.Sprintf("image-%d%s", time.Now().UnixNano(), filepath.Ext(header.Filename)))
		out, err := os.Create(filename)
		if err != nil {
			log.Printf("Error creating file: %v", err)
			c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "OCR processing failed"})
			return
		}
		defer out.Close()

		_, err = io.Copy(out, file)
		if err != nil {
			log.Printf("Error saving file: %v", err)
			c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "OCR processing failed"})
			return
		}
		filePath = filename
	} else if c.Request.ContentLength > 0 && strings.HasPrefix(c.ContentType(), "image/") {
		// Handle raw image body
		body, err := io.ReadAll(c.Request.Body)
		if err != nil {
			log.Printf("Error reading request body: %v", err)
			c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "OCR processing failed"})
			return
		}

		filename := filepath.Join(uploadDir, fmt.Sprintf("image-%d.png", time.Now().UnixNano()))
		if err := os.WriteFile(filename, body, 0644); err != nil {
			log.Printf("Error writing file: %v", err)
			c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "OCR processing failed"})
			return
		}
		filePath = filename
	} else {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "No image provided"})
		return
	}
	defer os.Remove(filePath)

	// Get OCR client from pool
	select {
	case client := <-app.OCRPool:
		defer func() { app.OCRPool <- client }()

		// Perform OCR
		if err := client.SetImage(filePath); err != nil {
			log.Printf("Error setting OCR image: %v", err)
			c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "OCR processing failed"})
			return
		}

		text, err := client.Text()
		if err != nil {
			log.Printf("OCR error: %v", err)
			c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "OCR processing failed"})
			return
		}

		log.Printf("OCR extracted text length: %d", len(text))

		// Increment prompt count
		_, err = app.incrementPromptCount(user.Email)
		if err != nil {
			log.Printf("Failed to update prompt count for: %s, error: %v", user.Email, err)
		}

		c.JSON(http.StatusOK, OCRResponse{Text: text})
	default:
		c.JSON(http.StatusServiceUnavailable, ErrorResponse{Error: "OCR service not ready yet. Please try again in a moment."})
	}
}

// handleAnalyze processes text analysis requests
func (app *App) handleAnalyze(c *gin.Context) {
	user, _, ok := app.verifyUserAndPromptLimit(c)
	if !ok {
		return
	}

	var req AnalyzeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Invalid request"})
		return
	}

	if req.Text == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "No text provided for analysis"})
		return
	}

	prompt := fmt.Sprintf("You are a coding assistant. Analyze this text from a coding question screenshot and provide a solution with code and explanation:\n\n%s\n\nFormat:\n```\n[Code]\n```\nExplanation: [Explanation]", req.Text)

	ctx := context.Background()
	resp, err := app.OpenAIClient.CreateChatCompletion(
		ctx,
		openai.ChatCompletionRequest{
			Model: openai.GPT4OMini,
			Messages: []openai.ChatCompletionMessage{
				{
					Role:    openai.ChatMessageRoleSystem,
					Content: "You are a coding assistant.",
				},
				{
					Role:    openai.ChatMessageRoleUser,
					Content: prompt,
				},
			},
			Temperature: 0.7,
			MaxTokens:   2048,
		},
	)

	if err != nil {
		log.Printf("AI analysis error: %v", err)
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "AI analysis failed"})
		return
	}

	aiResponse := resp.Choices[0].Message.Content
	log.Printf("AI response length: %d", len(aiResponse))

	// Increment prompt count
	_, err = app.incrementPromptCount(user.Email)
	if err != nil {
		log.Printf("Failed to update prompt count for: %s, error: %v", user.Email, err)
	}

	c.JSON(http.StatusOK, gin.H{"response": aiResponse})
}

// handleAudio processes audio transcription requests
func (app *App) handleAudio(c *gin.Context) {
	user, _, ok := app.verifyUserAndPromptLimit(c)
	if !ok {
		return
	}

	file, header, err := c.Request.FormFile("audio")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "No audio file provided"})
		return
	}

	// Create uploads directory if it doesn't exist
	uploadDir := "uploads"
	if _, err := os.Stat(uploadDir); os.IsNotExist(err) {
		if err := os.MkdirAll(uploadDir, 0755); err != nil {
			log.Printf("Error creating upload directory: %v", err)
			c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "Failed to process audio"})
			return
		}
	}

	filename := filepath.Join(uploadDir, fmt.Sprintf("audio-%d%s", time.Now().UnixNano(), filepath.Ext(header.Filename)))
	out, err := os.Create(filename)
	if err != nil {
		log.Printf("Error creating file: %v", err)
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "Failed to process audio"})
		return
	}
	defer out.Close()
	defer os.Remove(filename)

	_, err = io.Copy(out, file)
	if err != nil {
		log.Printf("Error saving file: %v", err)
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "Failed to process audio"})
		return
	}

	// Transcribe audio with OpenAI Whisper
	req := openai.AudioRequest{
		Model:    openai.Whisper1,
		FilePath: filename,
	}

	resp, err := app.OpenAIClient.CreateTranscription(context.Background(), req)
	if err != nil {
		log.Printf("Audio transcription error: %v", err)
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "Failed to process audio"})
		return
	}

	transcribedText := resp.Text

	// Process with GPT
	chatResp, err := app.OpenAIClient.CreateChatCompletion(
		context.Background(),
		openai.ChatCompletionRequest{
			Model: openai.GPT4OMini,
			Messages: []openai.ChatCompletionMessage{
				{
					Role:    openai.ChatMessageRoleSystem,
					Content: "Analyze audio transcriptions and respond concisely.",
				},
				{
					Role:    openai.ChatMessageRoleUser,
					Content: transcribedText,
				},
			},
			Temperature: 0.7,
			MaxTokens:   1000,
		},
	)

	if err != nil {
		log.Printf("AI analysis error: %v", err)
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "Failed to process audio"})
		return
	}

	aiResponse := chatResp.Choices[0].Message.Content

	// Increment prompt count
	_, err = app.incrementPromptCount(user.Email)
	if err != nil {
		log.Printf("Failed to update prompt count for: %s, error: %v", user.Email, err)
	}

	c.JSON(http.StatusOK, gin.H{
		"transcription": transcribedText,
		"analysis":      aiResponse,
	})
}

// handleChat processes chat requests
func (app *App) handleChat(c *gin.Context) {
	user, maxPrompts, ok := app.verifyUserAndPromptLimit(c)
	if !ok {
		return
	}

	var req ChatRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Invalid request"})
		return
	}

	if req.Prompt == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "No prompt provided"})
		return
	}

	resp, err := app.OpenAIClient.CreateChatCompletion(
		context.Background(),
		openai.ChatCompletionRequest{
			Model: openai.GPT4OMini,
			Messages: []openai.ChatCompletionMessage{
				{
					Role:    openai.ChatMessageRoleSystem,
					Content: "You are Grok 3 by xAI. Date: March 10, 2025.",
				},
				{
					Role:    openai.ChatMessageRoleUser,
					Content: req.Prompt,
				},
			},
			Temperature: 0.7,
			MaxTokens:   2048,
		},
	)

	if err != nil {
		log.Printf("Chat error: %v", err)
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "Chat processing failed"})
		return
	}

	chatResponse := resp.Choices[0].Message.Content

	// Increment prompt count
	updatedUser, err := app.incrementPromptCount(user.Email)
	if err != nil {
		log.Printf("Failed to update prompt count for: %s, error: %v", user.Email, err)
		updatedUser = user
	}

	c.JSON(http.StatusOK, gin.H{
		"response": chatResponse,
		"user": gin.H{
			"promptsUsed":      updatedUser.PromptsUsed,
			"remainingTries":   updatedUser.RemainingTries,
			"subscriptionType": updatedUser.SubscriptionType,
			"maxPrompts":       maxPrompts,
		},
	})
}

// handleUserStats gets user statistics
func (app *App) handleUserStats(c *gin.Context) {
	email := c.Param("email")
	if email == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Valid email is required"})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	var user User
	err := app.UserColl.FindOne(
		ctx,
		bson.M{"email": email},
		options.FindOne().SetProjection(bson.M{
			"_id":                0,
			"name":               1,
			"email":              1,
			"subscriptionType":   1,
			"subscriptionEndDate": 1,
			"remainingTries":     1,
			"promptsUsed":        1,
			"lastResetDate":      1,
		}),
	).Decode(&user)

	if err != nil {
		if err == mongo.ErrNoDocuments {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "User not found"})
		} else {
			log.Printf("User stats error: %v", err)
			c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "Failed to get user stats"})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{"user": user})
}

// handleHealthcheck checks the server health
func (app *App) handleHealthcheck(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := app.DB.Ping(ctx, nil); err == nil {
		c.JSON(http.StatusOK, gin.H{
			"status":     "healthy",
			"connection": "connected",
		})
		return
	}

	c.JSON(http.StatusServiceUnavailable, gin.H{
		"status":     "unhealthy",
		"connection": "disconnected",
	})
}

// handleCheckUpdate checks if an update is available
func (app *App) handleCheckUpdate(c *gin.Context) {
	clientVersion := c.Query("version")
	if clientVersion == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Client version is required"})
		return
	}

	isUpdateAvailable := compareVersions(clientVersion, app.Config.LatestVersion) < 0

	c.JSON(http.StatusOK, gin.H{
		"isUpdateAvailable": isUpdateAvailable,
		"clientVersion":     clientVersion,
		"latestVersion":     app.Config.LatestVersion,
		"message":           ternary(isUpdateAvailable, "An update is available. Please download the latest version.", "You are on the latest version. No update required."),
	})
}

// updateSubscriptionStatus updates the user's subscription status
func (app *App) updateSubscriptionStatus(user *User) (*User, error) {
	now := time.Now()
	needsUpdate := false

	if user.SubscriptionEndDate != nil && user.SubscriptionEndDate.After(now) {
		if user.SubscriptionType != "premium" {
			user.SubscriptionType = "premium"
			needsUpdate = true
		}
	} else {
		if user.SubscriptionType != "free" {
			user.SubscriptionType = "free"
			needsUpdate = true
		}
	}

	if needsUpdate {
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		filter := bson.M{"email": user.Email}
		update := bson.M{"$set": bson.M{"subscriptionType": user.SubscriptionType}}
		_, err := app.UserColl.UpdateOne(ctx, filter, update)
		if err != nil {
			return nil, err
		}
	}

	return user, nil
}

// resetPromptsIfNeeded resets prompts if conditions are met
func (app *App) resetPromptsIfNeeded(user *User) (*User, error) {
	now := time.Now()
	needsUpdate := false

	if user.SubscriptionType == "premium" && user.SubscriptionEndDate != nil && user.SubscriptionEndDate.After(now) {
		if user.LastResetDate.IsZero() {
			user.LastResetDate = now
			needsUpdate = true
			log.Printf("Set lastResetDate for %s to %v", user.Email, now)
		}

		lastReset := user.LastResetDate
		oneWeek := 7 * 24 * time.Hour
		if now.Sub(lastReset) >= oneWeek {
			user.PromptsUsed = 0
			user.LastResetDate = now
			needsUpdate = true
			log.Printf("Prompts reset for %s at %v", user.Email, now)
		}
	}

	if needsUpdate {
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		filter := bson.M{"email": user.Email}
		update := bson.M{
			"$set": bson.M{
				"promptsUsed":   user.PromptsUsed,
				"lastResetDate": user.LastResetDate,
			},
		}
		_, err := app.UserColl.UpdateOne(ctx, filter, update)
		if err != nil {
			return nil, err
		}
	}

	return user, nil
}

// incrementPromptCount increments the user's prompt count
func (app *App) incrementPromptCount(email string) (*User, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	filter := bson.M{"email": email}
	update := bson.M{"$inc": bson.M{"promptsUsed": 1}}
	opts := options.FindOneAndUpdate().SetReturnDocument(options.After)

	var updatedUser User
	err := app.UserColl.FindOneAndUpdate(ctx, filter, update, opts).Decode(&updatedUser)
	if err != nil {
		return nil, err
	}
	return &updatedUser, nil
}

// compareVersions compares two version strings
func compareVersions(v1, v2 string) int {
	v1Parts := strings.Split(v1, ".")
	v2Parts := strings.Split(v2, ".")

	for i := 0; i < len(v1Parts) || i < len(v2Parts); i++ {
		part1 := 0
		if i < len(v1Parts) {
			part1, _ = strconv.Atoi(v1Parts[i])
		}
		part2 := 0
		if i < len(v2Parts) {
			part2, _ = strconv.Atoi(v2Parts[i])
		}

		if part1 < part2 {
			return -1
		}
		if part1 > part2 {
			return 1
		}
	}
	return 0
}

// ternary is a simple ternary operator implementation
func ternary(condition bool, trueVal, falseVal string) string {
	if condition {
		return trueVal
	}
	return falseVal
}

// Add graceful shutdown handling
func init() {
	go func() {
		sigChan := make(chan os.Signal, 1)
		signal.Notify(sigChan, os.Interrupt, syscall.SIGTERM)
		<-sigChan

		log.Println("SIGTERM received, shutting down gracefully")
		// Cleanup is already handled by defer in main()
		os.Exit(0)
	}()
}