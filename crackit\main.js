const { app, BrowserWindow, ipcMain, globalShortcut, screen, systemPreferences, desktopCapturer, shell, dialog } = require('electron');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');
const io = require('socket.io-client');

require('dotenv').config();
const isDev = process.env.NODE_ENV === 'development';

let mainWindow, dashboardWindow, helperWindow;
let recordingProcess = null;
let isRecording = false;
let socket;

// Handle uncaught exceptions for better debugging
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

// Validate preload script existence
const preloadPath = path.join(__dirname, 'preload.js');
if (!fs.existsSync(preloadPath)) {
  console.error(`Preload script not found at: ${preloadPath}`);
} else {
  console.log(`Preload script located at: ${preloadPath}`);
}

function connectSocketIO() {
  socket = io('http://localhost:8080', { 
    reconnection: true, 
    reconnectionAttempts: 5, 
    reconnectionDelay: 1000 
  });

  socket.on('connect', () => {
    console.log('Socket.IO connected');
  });

  socket.on('transcription_result', (data) => {
    console.log('Received transcription result:', data);
    if (helperWindow && !helperWindow.isDestroyed()) helperWindow.webContents.send('transcription-result', data);
    if (dashboardWindow && !dashboardWindow.isDestroyed()) dashboardWindow.webContents.send('transcription-result', data);
  });

  socket.on('error', (data) => {
    console.error('Socket.IO error:', data);
  });

  socket.on('disconnect', () => {
    console.log('Socket.IO disconnected, attempting to reconnect...');
  });
}

async function requestMicPermission() {
  if (process.platform === 'darwin') {
    const micStatus = await systemPreferences.askForMediaAccess('microphone');
    console.log(`Microphone permission: ${micStatus}`);
    return micStatus === 'granted';
  } else if (process.platform === 'win32') {
    return true;
  }
  return true;
}

async function startRecording() {
  if (isRecording) {
    console.log('Recording already in progress');
    return false;
  }

  console.log('Starting Python recording process');
  isRecording = true;

  const pythonPath = process.platform === 'win32' ? 'python' : 'python3';
  recordingProcess = spawn(pythonPath, [path.join(__dirname, 'record.py')], {
    stdio: ['pipe', 'pipe', 'pipe'],
  });

  recordingProcess.stdout.setEncoding('utf8');
  recordingProcess.stderr.setEncoding('utf8');

  recordingProcess.stdout.on('data', (data) => {
    const lines = data.toString().trim().split('\n');
    lines.forEach(line => {
      console.log(`Recorder output: ${line}`);
      if (line && !line.includes('Initializing') && !line.includes('Command') && !line.includes('Recording') && !line.includes('Signal')) {
        if (socket && socket.connected) {
          socket.emit('audio_chunk', { data: line });
        } else {
          console.warn('Socket not connected, audio chunk not sent');
        }
        if (helperWindow && !helperWindow.isDestroyed()) helperWindow.webContents.send('audio-chunk', line);
        if (dashboardWindow && !dashboardWindow.isDestroyed()) dashboardWindow.webContents.send('audio-chunk', line);
      }
    });
  });

  recordingProcess.stderr.on('data', (data) => {
    console.error(`Recorder error: ${data.toString().trim()}`);
  });

  recordingProcess.on('error', (err) => {
    console.error('Process error:', err);
    isRecording = false;
    recordingProcess = null;
    if (helperWindow && !helperWindow.isDestroyed()) helperWindow.webContents.send('recording-stopped');
    if (dashboardWindow && !dashboardWindow.isDestroyed()) dashboardWindow.webContents.send('recording-stopped');
  });

  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (recordingProcess && recordingProcess.stdin) {
        recordingProcess.stdin.write('y\n');
        console.log('Recording started with "y" command');
        if (helperWindow && !helperWindow.isDestroyed()) helperWindow.webContents.send('recording-started');
        if (dashboardWindow && !dashboardWindow.isDestroyed()) dashboardWindow.webContents.send('recording-started');
        resolve(true);
      } else {
        reject(new Error('Recording process not initialized'));
        isRecording = false;
        if (helperWindow && !helperWindow.isDestroyed()) helperWindow.webContents.send('recording-stopped');
        if (dashboardWindow && !dashboardWindow.isDestroyed()) dashboardWindow.webContents.send('recording-stopped');
      }
    }, 500);
  });
}

async function stopRecording() {
  if (!isRecording || !recordingProcess) {
    console.log('No recording to stop');
    return { success: false, size: 0 };
  }

  return new Promise((resolve) => {
    if (recordingProcess.stdin) {
      recordingProcess.stdin.write('n\n');
      console.log('Sent "n" to stop recording');
    }

    const timeout = setTimeout(() => {
      console.log('Forcing process termination due to timeout');
      recordingProcess.kill('SIGTERM');
      isRecording = false;
      recordingProcess = null;
      if (helperWindow && !helperWindow.isDestroyed()) helperWindow.webContents.send('recording-stopped');
      if (dashboardWindow && !dashboardWindow.isDestroyed()) dashboardWindow.webContents.send('recording-stopped');
      resolve({ success: true, size: 0 });
    }, 3000);

    recordingProcess.on('close', (code) => {
      clearTimeout(timeout);
      console.log(`Recording process exited with code ${code}`);
      isRecording = false;
      recordingProcess = null;
      if (helperWindow && !helperWindow.isDestroyed()) helperWindow.webContents.send('recording-stopped');
      if (dashboardWindow && !dashboardWindow.isDestroyed()) dashboardWindow.webContents.send('recording-stopped');
      resolve({ success: true, size: 0 });
    });

    if (recordingProcess.stdin) {
      recordingProcess.stdin.end();
    }
  });
}

function createDashboardWindow() {
  console.log('Creating dashboard window');
  dashboardWindow = new BrowserWindow({
    width: 1600,
    height: 900,
    resizable: false,
    frame: true,
    skipTaskbar: true,
    webPreferences: {
      preload: preloadPath,
      contextIsolation: true,
      nodeIntegration: false,
      devTools: isDev,
    },
  });
  dashboardWindow.loadFile('dashboard/dashboard.html')
    .then(() => console.log('Dashboard window loaded'))
    .catch(err => console.error('Failed to load dashboard window:', err));
  dashboardWindow.setContentProtection(true);
  if (process.platform === 'darwin') {
    dashboardWindow.setHiddenInMissionControl(true);
    dashboardWindow.setWindowButtonVisibility(false);
  }
  if (isDev) dashboardWindow.webContents.openDevTools();
  dashboardWindow.on('closed', () => {
    console.log('Dashboard window closed');
    dashboardWindow = null;
  });
  dashboardWindow.webContents.on('preload-error', (event, preloadPath, error) => {
    console.error(`Preload error for dashboard: ${preloadPath}`, error);
  });
}

function createHelperWindow() {
  console.log('Creating helper window');
  helperWindow = new BrowserWindow({
    width: 850,
    height: 500,
    x: 50,
    y: 50,
    frame: false,
    transparent: true,
    alwaysOnTop: true,
    skipTaskbar: true,
    webPreferences: {
      preload: preloadPath,
      contextIsolation: true,
      nodeIntegration: false,
      devTools: isDev,
    },
  });
  helperWindow.loadFile('index.html')
    .then(() => console.log('Helper window loaded'))
    .catch(err => console.error('Failed to load helper window:', err));
  helperWindow.setContentProtection(false);
  helperWindow.setVisibleOnAllWorkspaces(true, { visibleOnFullScreen: true });
  if (process.platform === 'darwin') {
    helperWindow.setHiddenInMissionControl(true);
    helperWindow.setWindowButtonVisibility(false);
  }
  helperWindow.setOpacity(1);
  if (isDev) helperWindow.webContents.openDevTools();
  helperWindow.on('closed', () => {
    console.log('Helper window closed');
    helperWindow = null;
    if (dashboardWindow && !dashboardWindow.isDestroyed()) {
      dashboardWindow.show();
      dashboardWindow.focus();
    }
  });
  if (!isDev) {
    helperWindow.webContents.on('devtools-opened', () => helperWindow.webContents.closeDevTools());
  }
  helperWindow.webContents.on('preload-error', (event, preloadPath, error) => {
    console.error(`Preload error for helper: ${preloadPath}`, error);
  });
  console.log('Helper window created successfully');
}

ipcMain.handle('show-message-box', async (event, options) => {
  const targetWindow = dashboardWindow || helperWindow || mainWindow;
  return targetWindow ? await dialog.showMessageBox(targetWindow, options) : await dialog.showMessageBox(options);
});

ipcMain.on('open-external-link', (event, url) => shell.openExternal(url));

ipcMain.on('quit-app', () => app.quit());

ipcMain.handle('request-mic-permission', async () => await requestMicPermission());

ipcMain.handle('capture-screen', async () => {
  try {
    const primaryDisplay = screen.getPrimaryDisplay();
    const { width, height } = primaryDisplay.size;
    const sources = await desktopCapturer.getSources({
      types: ['screen'],
      thumbnailSize: { width, height },
    });
    const source = sources.find(s => s.display_id === primaryDisplay.id.toString()) || sources[0];
    return source.thumbnail.toPNG();
  } catch (error) {
    console.error('Screen capture error:', error);
    return null;
  }
});

ipcMain.handle('close-window', () => {
  if (helperWindow && !helperWindow.isDestroyed()) {
    helperWindow.close();
    return true;
  }
  return false;
});

ipcMain.handle('move-window', (event, x, y) => {
  if (helperWindow && !helperWindow.isDestroyed()) {
    const validX = Number.isFinite(x) ? Math.round(x) : 0;
    const validY = Number.isFinite(y) ? Math.round(y) : 0;
    helperWindow.setPosition(validX, validY);
    return [validX, validY];
  }
  return false;
});

ipcMain.handle('resize-window', (event, width, height) => {
  if (helperWindow && !helperWindow.isDestroyed()) {
    const w = Math.max(Math.round(Number.isFinite(width) ? width : 300), 300);
    const h = Math.max(Math.round(Number.isFinite(height) ? height : 200), 200);
    helperWindow.setSize(w, h);
    return { width: w, height: h };
  }
  return false;
});

ipcMain.handle('get-window-position', () => {
  return helperWindow && !helperWindow.isDestroyed() ? helperWindow.getPosition() : { x: 0, y: 0 };
});

ipcMain.handle('get-window-size', () => {
  return helperWindow && !helperWindow.isDestroyed() ? helperWindow.getSize() : { width: 400, height: 300 };
});

ipcMain.handle('toggle-window', () => {
  if (helperWindow && !helperWindow.isDestroyed()) {
    const currentOpacity = helperWindow.getOpacity();
    helperWindow.setOpacity(currentOpacity === 1 ? 0 : 1);
    return true;
  }
  return false;
});

ipcMain.handle('launch-helper', async () => {
  console.log('Launch helper requested');
  if (helperWindow && !helperWindow.isDestroyed()) {
    console.log('Helper window already exists and is not destroyed');
    await dialog.showMessageBox({
      type: 'info',
      title: 'Helper Already Open',
      message: 'A helper window is already open. Please close it before launching a new one.',
      buttons: ['OK'],
    });
    return false;
  }
  console.log('No existing helper window, creating new one');
  createHelperWindow();
  if (dashboardWindow && !dashboardWindow.isDestroyed()) {
    console.log('Hiding dashboard window');
    dashboardWindow.hide();
  }
  return true;
});

ipcMain.handle('save-to-file', async (event, content) => {
  try {
    const { filePath } = await dialog.showSaveDialog({
      defaultPath: `result-${new Date().toISOString().slice(0,19).replace(/:/g, '-')}.txt`,
      filters: [{ name: 'Text Files', extensions: ['txt'] }],
    });
    if (filePath) {
      fs.writeFileSync(filePath, content);
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error saving file:', error);
    return false;
  }
});

ipcMain.handle('start-recording', async () => {
  try {
    return await startRecording();
  } catch (error) {
    console.error('Start recording failed:', error);
    return false;
  }
});

ipcMain.handle('stop-recording', async () => {
  return await stopRecording();
});

// Credential handlers
ipcMain.handle('get-credentials', async () => {
  try {
    const credentialsPath = path.join(app.getPath('userData'), 'credentials.json');
    if (fs.existsSync(credentialsPath)) {
      const data = fs.readFileSync(credentialsPath, 'utf8');
      console.log('Retrieved credentials from:', credentialsPath);
      return JSON.parse(data);
    }
    console.log('No credentials found at:', credentialsPath);
    return null; // Or return default credentials if applicable
  } catch (error) {
    console.error('Error getting credentials:', error);
    return null;
  }
});

ipcMain.handle('save-credentials', async (event, credentials) => {
  try {
    const credentialsPath = path.join(app.getPath('userData'), 'credentials.json');
    fs.writeFileSync(credentialsPath, JSON.stringify(credentials, null, 2));
    console.log('Credentials saved to:', credentialsPath);
    return true;
  } catch (error) {
    console.error('Error saving credentials:', error);
    return false;
  }
});

function registerShortcuts() {
  globalShortcut.register('CommandOrControl+Shift+C', () => {
    if (helperWindow && !helperWindow.isDestroyed()) helperWindow.webContents.send('trigger-capture');
  });

  globalShortcut.register('CommandOrControl+Shift+P', () => {
    if (helperWindow && !helperWindow.isDestroyed()) helperWindow.webContents.send('trigger-process');
  });

  globalShortcut.register('CommandOrControl+Shift+T', () => {
    if (helperWindow && !helperWindow.isDestroyed()) {
      const currentOpacity = helperWindow.getOpacity();
      helperWindow.setOpacity(currentOpacity === 1 ? 0 : 1);
    }
  });

  globalShortcut.register('CommandOrControl+Shift+D', () => {
    if (dashboardWindow && !dashboardWindow.isDestroyed()) {
      dashboardWindow.show();
      dashboardWindow.focus();
    } else {
      createDashboardWindow();
    }
  });

  globalShortcut.register('CommandOrControl+Shift+R', () => {
    if (isRecording) {
      stopRecording().then((result) => {
        if (helperWindow && !helperWindow.isDestroyed()) helperWindow.webContents.send('recording-stopped');
        if (dashboardWindow && !dashboardWindow.isDestroyed()) dashboardWindow.webContents.send('recording-stopped');
      });
    } else {
      startRecording()
        .then(() => {
          if (helperWindow && !helperWindow.isDestroyed()) helperWindow.webContents.send('recording-started');
          if (dashboardWindow && !dashboardWindow.isDestroyed()) dashboardWindow.webContents.send('recording-started');
        })
        .catch((err) => {
          console.error('Recording failed in shortcut handler:', err);
          if (helperWindow && !helperWindow.isDestroyed()) helperWindow.webContents.send('recording-stopped');
          if (dashboardWindow && !dashboardWindow.isDestroyed()) dashboardWindow.webContents.send('recording-stopped');
        });
    }
  });

  globalShortcut.register('CommandOrControl+Shift+M', () => {
    if (dashboardWindow && !dashboardWindow.isDestroyed()) dashboardWindow.minimize();
  });

  globalShortcut.register('CommandOrControl+Shift+H', () => {
    if (helperWindow && !helperWindow.isDestroyed()) helperWindow.hide();
  });

  globalShortcut.register('CommandOrControl+Shift+S', () => {
    if (helperWindow && !helperWindow.isDestroyed()) {
      helperWindow.show();
      helperWindow.focus();
    }
  });
}

app.whenReady().then(async () => {
  try {
    console.log('App is ready, initializing...');
    const hasMicPermission = await requestMicPermission();
    if (!hasMicPermission) console.log('Microphone permission denied');
    console.log('Creating dashboard window...');
    createDashboardWindow();
    console.log('Connecting to WebSocket...');
    connectSocketIO();
    console.log('Registering shortcuts...');
    registerShortcuts();
    console.log('Initialization complete');
  } catch (error) {
    console.error('Error during app initialization:', error);
  }
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit();
});

app.on('activate', () => {
  if (!dashboardWindow && !helperWindow) createDashboardWindow();
});

app.on('will-quit', () => {
  globalShortcut.unregisterAll();
  if (socket) socket.disconnect();
});

app.on('web-contents-created', (event, contents) => {
  if (!isDev) {
    contents.on('devtools-opened', () => contents.closeDevTools());
    contents.on('before-input-event', (event, input) => {
      if (input.type === 'keyDown' && (
        input.key === 'F12' ||
        (input.control && input.shift && input.key === 'I') ||
        (input.meta && input.alt && input.key === 'I')
      )) {
        event.preventDefault();
      }
    });
  }
});