:root {
    --primary-color: #00A896;
    --secondary-color: #1a252f;
    --text-color: #d1d5db;
    --hover-color: #028090;
    --bg-opacity: 0.95;
    --border-radius: 8px;
    --panel-width: 300px;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --glass-bg: rgba(30, 30, 30, 0.8);
    --glass-blur: blur(10px);
    --title-bar-height: 32px;
    --shortcuts-height: 60px;
    --tabs-height: 40px;
    --chat-header-height: 30px;
    --chat-input-height: 60px;
    --status-bar-height: 30px;
}

html, body {
    height: 100vh;
    margin: 0;
    padding: 12px;
    box-sizing: border-box;
    background: rgba(23, 33, 43, var(--bg-opacity));
    color: var(--text-color);
    font-family: 'Roboto', sans-serif;
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 255, 255, 0.05);
    overflow: hidden;
    user-select: none;
    display: flex;
    flex-direction: column;
}

.title-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    -webkit-app-region: drag;
    padding: 0;
    height: var(--title-bar-height);
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: var(--glass-blur);
    border-radius: var(--border-radius);
}

.title-bar h3 {
    margin: 0;
    font-size: 16px;
    padding-left: 10px;
    font-weight: 500;
}

.close-btn {
    position: absolute;
    top: 0;
    right: 0;
    width: 46px;
    height: var(--title-bar-height);
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: transparent;
    color: #aaa;
    border: none;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.15s ease, transform 0.2s;
    -webkit-app-region: no-drag;
}

.close-btn:hover {
    background-color: #e53935;
    color: white;
    transform: scale(1.05);
}

.close-btn:active {
    background-color: #d32f2f;
}

.container {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    border-radius: var(--border-radius);
}

.main-content {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    flex: 1;
}

.shortcuts {
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: var(--glass-blur);
    padding: 8px;
    border-radius: var(--border-radius);
    margin-bottom: 10px;
    font-size: 12px;
    height: var(--shortcuts-height);
    box-sizing: border-box;
}

.shortcut-key {
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 3px;
    margin: 0 2px;
    display: inline-block;
}

.tabs {
    display: flex;
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: var(--glass-blur);
    padding: 4px;
    border-radius: var(--border-radius);
    margin-bottom: 10px;
    height: var(--tabs-height);
    box-sizing: border-box;
}

.tab {
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s, transform 0.2s;
    margin-right: 4px;
}

.tab.active {
    background-color: var(--primary-color);
    color: white;
    transform: scale(1.02);
}

.tab:not(.active):hover {
    background-color: rgba(0, 168, 150, 0.2);
    transform: scale(1.02);
}

.tab-content {
    display: none;
    flex-direction: column;
    overflow: hidden;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 10px var(--shadow-color);
    flex: 1;
}

.tab-content.active {
    display: flex;
}

.chat-section {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    flex: 1;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 10px;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: var(--glass-blur);
    border-top-left-radius: var(--border-radius);
    border-top-right-radius: var(--border-radius);
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    height: var(--chat-header-height);
    box-sizing: border-box;
    flex-shrink: 0;
}

.chat-messages {
    padding: 20px;
    overflow-y: auto !important; /* Ensure scrolling works */
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: calc(100% - var(--chat-header-height) - var(--chat-input-height) - var(--status-bar-height) - 20px);
    scrollbar-width: thin;
    scrollbar-color: rgba(100, 100, 100, 0.5) rgba(30, 30, 30, 0.1);
    flex: 1; /* Ensure it takes available space */
    min-height: 0; /* Prevent overflow issues */
    scroll-behavior: auto; /* Change from smooth if it's too slow */
    --webkit-overflow-scrolling: touch;
}

.chat-messages::-webkit-scrollbar {
    width: 8px;
}

.chat-messages::-webkit-scrollbar-track {
    background: rgba(30, 30, 30, 0.1);
    border-radius: 4px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: rgba(100, 100, 100, 0.5);
    border-radius: 4px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: rgba(120, 120, 120, 0.7);
}

@supports (-moz-appearance:none) {
  .chat-messages {
    scrollbar-width: thin;
    scrollbar-color: rgba(100, 100, 100, 0.5) rgba(30, 30, 30, 0.1);
    
    /* Firefox-specific scroll speed - lower values = faster scrolling */
    scrollbar-width: thin;
    scrollbar-color: rgba(100, 100, 100, 0.5) rgba(30, 30, 30, 0.1);
  }
}

.message {
    padding: 12px 16px;
    border-radius: var(--border-radius);
    max-width: 70%;
    word-wrap: break-word;
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.3s, transform 0.3s;
    box-shadow: 0 2px 5px var(--shadow-color);
}

.message.visible {
    opacity: 1;
    transform: translateY(0);
}

.user-message {
    align-self: flex-end;
    background-color: var(--primary-color);
    color: white;
}

.ai-message {
    align-self: flex-start;
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text-color);
}

.message-time {
    font-size: 10px;
    opacity: 0.7;
    margin-top: 4px;
    text-align: right;
}

.message-image {
    max-width: 100%;
    border-radius: var(--border-radius);
    margin-bottom: 8px;
    box-shadow: 0 2px 5px var(--shadow-color);
    transition: transform 0.2s ease;
}

.message-image:hover {
    transform: scale(1.02);
}

.chat-input-container {
    display: flex;
    padding: 10px;
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: var(--glass-blur);
    border-bottom-left-radius: var(--border-radius);
    border-bottom-right-radius: var(--border-radius);
    gap: 8px;
    flex-shrink: 0;
    box-shadow: 0 -2px 5px var(--shadow-color);
    height: var(--chat-input-height);
    box-sizing: border-box;
    margin-bottom: 10px;
}

/* Updated styles for language and model dropdowns */
.language-select, .model-select {
    background: rgba(0, 0, 0, 0.3); /* Darker background with glassmorphism */
    backdrop-filter: var(--glass-blur); /* Glassmorphism effect */
    border: 1px solid rgba(255, 255, 255, 0.1); /* Subtle border */
    color: var(--text-color); /* White text */
    padding: 8px 12px; /* Consistent padding */
    border-radius: var(--border-radius); /* Match border-radius with UI */
    outline: none; /* Remove default outline */
    font-family: 'Roboto', sans-serif; /* Match font */
    font-size: 14px; /* Consistent font size */
    transition: border-color 0.2s ease, transform 0.2s ease, background-color 0.2s ease; /* Smooth transitions */
    cursor: pointer; /* Indicate interactivity */
    appearance: none; /* Remove default browser styling */
    -webkit-appearance: none; /* For Safari */
    -moz-appearance: none; /* For Firefox */
    position: relative; /* For custom arrow */
    background-image: url('data:image/svg+xml;utf8,<svg fill="none" height="24" width="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5H7z" fill="white"/></svg>'); /* Custom white dropdown arrow */
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px;
}

/* Hover state */
.language-select:hover, .model-select:hover {
    background: rgba(0, 0, 0, 0.5); /* Slightly lighter on hover */
    border-color: var(--hover-color); /* Use theme hover color */
    transform: scale(1.02); /* Subtle scale effect */
}

/* Focus state */
.language-select:focus, .model-select:focus {
    border-color: var(--primary-color); /* Highlight with primary color */
    transform: scale(1.02); /* Subtle scale effect */
    background: rgba(0, 0, 0, 0.5); /* Slightly lighter background */
}

/* Style for dropdown options (limited support, works in some browsers) */
.language-select option, .model-select option {
    background: var(--secondary-color); /* Dark background for options */
    color: var(--text-color); /* White text for options */
    font-family: 'Roboto', sans-serif;
    padding: 8px;
}

/* Firefox-specific styling for the dropdown arrow */
@-moz-document url-prefix() {
    .language-select, .model-select {
        background-image: url('data:image/svg+xml;utf8,<svg fill="none" height="24" width="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5H7z" fill="white"/></svg>');
        background-repeat: no-repeat;
        background-position: right 8px center;
        background-size: 16px;
    }
}

.chat-input {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-color);
    padding: 8px 12px;
    border-radius: 4px;
    outline: none;
    transition: border-color 0.2s, transform 0.2s;
}

.chat-input:focus {
    border-color: var(--primary-color);
    transform: scale(1.02);
}

button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s, transform 0.2s;
    font-weight: 500;
    -webkit-app-region: no-drag;
}

button:hover {
    background-color: var(--hover-color);
    transform: scale(1.05);
}

button:disabled {
    background-color: #555;
    cursor: not-allowed;
    transform: none;
}

.audio-container {
    background: var(--glass-bg);
    padding: 10px;
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 255, 255, 0.1);
    flex: 1;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 10px var(--shadow-color);
}

.recording-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 6px 10px;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: var(--glass-blur);
    border-top-left-radius: var(--border-radius);
    border-top-right-radius: var(--border-radius);
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}

.recording-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    padding: 0 10px;
}

.recording-info {
    font-size: 12px;
    color: #aaa;
    padding: 10px;
    flex: 1;
    overflow-y: auto;
    background: rgba(0, 0, 0, 0.2);
    border-radius: var(--border-radius);
}

.status-bar {
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: var(--glass-blur);
    padding: 8px;
    border-radius: var(--border-radius);
    display: flex;
    justify-content: space-between;
    box-shadow: 0 -2px 5px var(--shadow-color);
    height: var(--status-bar-height);
    box-sizing: border-box;
    flex-shrink: 0;
}

#status {
    font-weight: 500;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-overlay i {
    font-size: 48px;
    margin-bottom: 20px;
    color: var(--primary-color);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-message {
    font-size: 24px;
    margin-bottom: 10px;
    color: var(--text-color);
}

.sub-message {
    font-size: 16px;
    color: #aaa;
}

.markdown-content {
    line-height: 1.6;
}

.markdown-content pre {
    background: rgba(0, 0, 0, 0.3);
    padding: 10px;
    border-radius: 4px;
    overflow-x: auto;
}

.markdown-content code {
    background: rgba(0, 0, 0, 0.3);
    padding: 2px 4px;
    border-radius: 3px;
}

.shortcuts-container {
    padding: 10px;
}

.shortcuts-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.shortcuts-table th, .shortcuts-table td {
    padding: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    text-align: left;
}

.shortcuts-table th {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: var(--glass-blur);
}

/* Mermaid diagram styles */
.mermaid {
    background: rgba(255, 255, 255, 0.1);
    padding: 10px;
    border-radius: var(--border-radius);
    margin-top: 10px;
    overflow-x: auto;
    display: block;
    min-height: 50px;
  }
  .mermaid svg {
    max-width: 100%;
    height: auto;
    fill: var(--text-color);
    stroke: var(--text-color);
    background: transparent;
  }
  .mermaid svg text {
    fill: var(--text-color);
  }
  .mermaid svg path, .mermaid svg line {
    stroke: var(--text-color);
  }