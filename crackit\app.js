// app.js

// Global variables
let lastImageBuffer = null;
let windowPosition = { x: 50, y: 50 };
let windowSize = { width: 850, height: 500 };
const moveStep = 30; // Increased from 10 to 20 for faster movement
const resizeStep = 30;
const zoomStep = 20;
const scrollStep = 100;
let transcription = '';
const MAX_MEMORY_SIZE = 10; // Limit memory to 10 previous prompts
let assistantConversationHistory = []; // Conversation history for Assistant tab
let systemDesignConversationHistory = []; // Conversation history for System Design tab

function appLog(message) {
  console.log(`[APP] ${message}`);
}

function isModifierKey(event) {
  if (!window.ui || typeof window.ui.isMac === 'undefined') {
    appLog("Error: window.ui.isMac not available, defaulting to Ctrl");
    return event.ctrlKey;
  }
  return window.ui.isMac ? event.metaKey : event.ctrlKey;
}

function toggleRecordingButtons(isRecording) {
  const startBtn = document.getElementById('startRecordingBtn');
  const stopBtn = document.getElementById('stopRecordingBtn');
  if (startBtn && stopBtn) {
    startBtn.disabled = isRecording;
    stopBtn.disabled = !isRecording;
    const statusElement = document.getElementById('status');
    const recordingStatusElement = document.getElementById('recording-status');
    if (statusElement) statusElement.textContent = isRecording ? 'Recording...' : 'Ready';
    if (recordingStatusElement) recordingStatusElement.textContent = isRecording ? 'Recording...' : 'Ready';
  } else {
    appLog("Error: Recording buttons not found");
  }
}

// Utility function to limit history size (modifies the array in place)
function limitHistory(history) {
  if (history.length > MAX_MEMORY_SIZE * 2) { // *2 because each prompt has user and assistant response
    history.splice(0, history.length - MAX_MEMORY_SIZE * 2); // Remove oldest entries
  }
  return history; // Return the modified array for chaining if needed
}

// Assistant tab: Send message
function sendAssistantMessage() {
  const inputElement = document.getElementById('chat-input');
  const languageSelect = document.getElementById('language-select');
  const modelSelect = document.getElementById('model-select');
  if (!inputElement || !languageSelect || !modelSelect) {
    appLog("Error: #chat-input, #language-select, or #model-select not found");
    return;
  }
  const message = inputElement.value.trim();
  const language = languageSelect.value;
  const model = modelSelect.value;
  if (message) {
    if (window.ui && window.ui.addChatMessage) {
      window.ui.addChatMessage('chat-messages', message, true);
    } else {
      appLog("Error: window.ui.addChatMessage not available");
      return;
    }
    // Store raw user message in history
    assistantConversationHistory.push({ role: 'user', content: message });
    limitHistory(assistantConversationHistory); // Limit history (modifies in place)
    appLog(`Assistant conversation history after adding user message: ${JSON.stringify(assistantConversationHistory)}`);
    inputElement.value = '';
    const chatStatus = document.getElementById('chat-status');
    if (chatStatus) chatStatus.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Thinking...';
    // Send raw message as prompt; server handles language and model
    processUserMessage('chat-messages', assistantConversationHistory, message, model, language, false);
  }
}

// System Design tab: Send message
function sendSystemDesignMessage() {
  const inputElement = document.getElementById('system-design-input');
  const modelSelect = document.getElementById('system-design-model-select');
  if (!inputElement || !modelSelect) {
    appLog("Error: #system-design-input or #system-design-model-select not found");
    return;
  }
  const message = inputElement.value.trim();
  const model = modelSelect.value;
  if (message) {
    const systemDesignPrompt = `
    You are an expert in system design. For the following system design problem: "${message}", follow these steps:
    
    1. **Provide a Diagram in Mermaid Syntax**:
       - Create a detailed diagram to visualize the architecture (e.g., sequence diagram, component diagram, or architecture diagram).
       - Use a code block with the "mermaid" language identifier, like this:
         \`\`\`mermaid
         graph TD
         A[Client] --> B[Load Balancer]
         B --> C[Application Server]
         \`\`\`
       - **Mermaid Syntax Requirements**:
         - Use only the following arrow types: --> for a standard arrow, --- for no arrow, -.- for a dotted line, or ==> for a thick arrow.
         - Do not use unsupported arrow types like arrow_open, arrow_point, or any other custom arrow types.
         - Ensure all labels are enclosed in |label| format, e.g., -->|HTTP Request|.
         - Use subgraphs to group related components (e.g., frontend, backend, database, external services) for clarity.
         - Add colors to nodes and edges using Mermaid's styling syntax to enhance visual clarity. Ensure text is readable on all nodes by setting appropriate fill colors for text.
         - **Color Guidelines**:
           - Use the following color scheme for nodes:
             - Frontend nodes (e.g., Client): fill:#4a90e2 (a vibrant blue), text: white.
             - Backend nodes (e.g., Load Balancer, Application Server, Web Server): fill:#50c878 (a bright green), text: white.
             - Database nodes: fill:#e57373 (a deeper red), text: white.
             - External services (e.g., Authentication Service, Third-Party APIs): fill:#f4a261 (a warm orange), text: white.
           - Use the following colors for edges:
             - HTTP/HTTPS connections: stroke:#ff6f61 (coral red), stroke-width:2px.
             - Database queries: stroke:#4dd0e1 (cyan), stroke-width:2px.
             - API calls or external service interactions: stroke:#ffd700 (gold), stroke-width:2px.
         - Example with updated colors:
           \`\`\`mermaid
           graph TD
           subgraph Frontend
             A[Client]
           end
           subgraph Backend
             B[Load Balancer] -->|HTTP/HTTPS| C[Web Server]
             C -->|API Calls| D[Application Server]
           end
           subgraph Database
             E[Database]
           end
           subgraph External_Services
             F[Authentication Service]
             G[Third-Party APIs]
           end
           A --> B
           D -->|Database Query| E
           D -->|Auth Check| F
           D -->|External Data| G
           classDef frontend fill:#4a90e2,stroke:#333,stroke-width:2px,color:#ffffff;
           classDef backend fill:#50c878,stroke:#333,stroke-width:2px,color:#ffffff;
           classDef database fill:#e57373,stroke:#333,stroke-width:2px,color:#ffffff;
           classDef external fill:#f4a261,stroke:#333,stroke-width:2px,color:#ffffff;
           class A frontend;
           class B,C,D backend;
           class E database;
           class F,G external;
           linkStyle 0 stroke:#ff6f61,stroke-width:2px;
           linkStyle 1 stroke:#ffd700,stroke-width:2px;
           linkStyle 2 stroke:#4dd0e1,stroke-width:2px;
           linkStyle 3 stroke:#ffd700,stroke-width:2px;
           \`\`\`
    
    2. **Provide a Detailed Explanation**:
       - Include the following sections:
         1. A step-by-step breakdown of the system architecture.
         2. Key components and their interactions.
         3. Scalability, reliability, and performance considerations.
       - Ensure the explanation is clear, concise, and tailored to the problem.
    
    Example response format:
    \`\`\`mermaid
    graph TD
    subgraph Frontend
      A[Client]
    end
    subgraph Backend
      B[Load Balancer] -->|HTTP/HTTPS| C[Web Server]
      C -->|API Calls| D[Application Server]
    end
    subgraph Database
      E[Database]
    end
    subgraph External_Services
      F[Authentication Service]
      G[Third-Party APIs]
    end
    A --> B
    D -->|Database Query| E
    D -->|Auth Check| F
    D -->|External Data| G
    classDef frontend fill:#4a90e2,stroke:#333,stroke-width:2px,color:#ffffff;
    classDef backend fill:#50c878,stroke:#333,stroke-width:2px,color:#ffffff;
    classDef database fill:#e57373,stroke:#333,stroke-width:2px,color:#ffffff;
    classDef external fill:#f4a261,stroke:#333,stroke-width:2px,color:#ffffff;
    class A frontend;
    class B,C,D backend;
    class E database;
    class F,G external;
    linkStyle 0 stroke:#ff6f61,stroke-width:2px;
    linkStyle 1 stroke:#ffd700,stroke-width:2px;
    linkStyle 2 stroke:#4dd0e1,stroke-width:2px;
    linkStyle 3 stroke:#ffd700,stroke-width:2px;
    \`\`\`
    
    ### Explanation
    #### Step-by-Step Breakdown
    1. The client sends an HTTP/HTTPS request to the load balancer.
    2. The load balancer distributes the request to a web server.
    3. The web server forwards the request to an application server via API calls.
    4. The application server queries the database for data.
    5. The application server performs an authentication check with the authentication service.
    6. The application server fetches external data from third-party APIs.
    7. The response is sent back through the web server and load balancer to the client.
    
    #### Key Components and Interactions
    - **Client**: Initiates requests to the system.
    - **Load Balancer**: Distributes incoming traffic across multiple web servers.
    - **Web Server**: Handles initial request processing and forwards to the application server.
    - **Application Server**: Manages business logic, queries the database, and interacts with external services.
    - **Database**: Stores and retrieves data as requested.
    - **Authentication Service**: Validates user credentials.
    - **Third-Party APIs**: Provide external data or functionality.
    
    #### Scalability, Reliability, and Performance Considerations
    - **Scalability**: The load balancer enables horizontal scaling by adding more web and application servers. The database can be sharded or replicated to handle increased load.
    - **Reliability**: Use redundant load balancers, web servers, and application servers to avoid single points of failure. Implement database replication for fault tolerance.
    - **Performance**: Optimize database queries and use caching (e.g., Redis) to reduce load on the database. Ensure the load balancer uses efficient routing algorithms. Minimize latency in external API calls by caching responses where possible.
    `;
    if (window.ui && window.ui.addChatMessage) {
      window.ui.addChatMessage('system-design-messages', message, true);
    } else {
      appLog("Error: window.ui.addChatMessage not available");
      return;
    }
    // Store only the raw user message in history
    systemDesignConversationHistory.push({ role: 'user', content: message });
    limitHistory(systemDesignConversationHistory); // Limit history (modifies in place)
    appLog(`System design conversation history after adding user message: ${JSON.stringify(systemDesignConversationHistory)}`);
    inputElement.value = '';
    const systemDesignStatus = document.getElementById('system-design-status');
    if (systemDesignStatus) systemDesignStatus.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Thinking...';
    // Language is not relevant for system design, so pass null
    processUserMessage('system-design-messages', systemDesignConversationHistory, systemDesignPrompt, model, null, true);
  }
}

async function processUserMessage(containerId, conversationHistory, message, model, language, isSystemDesign) {
  try {
    const credentials = JSON.parse(localStorage.getItem('helperCredentials'));
    if (!credentials || !credentials.email || !credentials.secretKey) {
      throw new Error('Missing credentials');
    }
    appLog(`Sending request to server with prompt: ${message}, model: ${model}, language: ${language}`);
    limitHistory(conversationHistory); // Limit history before sending (modifies in place)
    appLog(`Conversation history being sent: ${JSON.stringify(conversationHistory)}`);
    const response = await fetch('http://localhost:8080/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-secret-key': credentials.secretKey,
      },
      body: JSON.stringify({
        conversation: conversationHistory, // Send the history directly
        prompt: message,
        model: model,
        language: language, // Include language in the request
        email: credentials.email,
      }),
    });
    appLog(`Received response from server with status: ${response.status}`);
    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
    const data = await response.json();
    appLog(`Server response data: ${JSON.stringify(data)}`);
    let aiResponse = data.response || 'No response received from server';

    conversationHistory.push({ role: 'assistant', content: aiResponse });
    limitHistory(conversationHistory); // Limit history after response (modifies in place)
    appLog(`Updated conversation history: ${JSON.stringify(conversationHistory)}`);

    if (window.ui && window.ui.addChatMessage) {
      window.ui.addChatMessage(containerId, aiResponse, false);
    }

    const statusElement = document.getElementById(isSystemDesign ? 'system-design-status' : 'chat-status');
    if (statusElement) statusElement.textContent = 'Ready';
  } catch (error) {
    console.error("Error processing message:", error);
    const statusElement = document.getElementById(isSystemDesign ? 'system-design-status' : 'chat-status');
    if (statusElement) statusElement.textContent = 'Error';
    if (window.ui && window.ui.addChatMessage) {
      window.ui.addChatMessage(containerId, `Sorry, there was an error: ${error.message}`);
    }
  }
}

function scrollActiveTab(direction) {
  const activeTab = document.querySelector('.tab.active');
  if (!activeTab) return;
  const tabId = activeTab.getAttribute('data-tab');
  let scrollElement;
  if (tabId === 'assistant') {
    scrollElement = document.getElementById('chat-messages');
  } else if (tabId === 'system-design') {
    scrollElement = document.getElementById('system-design-messages');
  } else if (tabId === 'audio') {
    scrollElement = document.getElementById('recording-info');
  } else if (tabId === 'shortcuts') {
    scrollElement = document.getElementById('shortcuts-tab');
  }
  if (scrollElement) {
    const scrollAmount = direction === 'up' ? -scrollStep : scrollStep;
    scrollElement.scrollBy({ top: scrollAmount, behavior: 'smooth' });
    appLog(`Scrolling ${tabId} tab by ${scrollAmount}px`);
  }
}

async function moveWindow(deltaX, deltaY) {
  try {
    if (!window.electronAPI) {
      appLog("Error: window.electronAPI not available for moveWindow");
      return false;
    }
    const position = await window.electronAPI.getWindowPosition();
    if (position && typeof position.x === 'number' && typeof position.y === 'number') windowPosition = position;
    const newX = windowPosition.x + deltaX;
    const newY = windowPosition.y + deltaY;
    appLog(`Moving window from (${windowPosition.x}, ${windowPosition.y}) to (${newX}, ${newY})`);
    const result = await window.electronAPI.moveWindow(newX, newY);
    if (result) {
      windowPosition.x = newX;
      windowPosition.y = newY;
      return true;
    }
    return false;
  } catch (error) {
    console.error("Error moving window:", error);
    return false;
  }
}

async function resizeWindow(width, height) {
  try {
    if (!window.electronAPI) {
      appLog("Error: window.electronAPI not available for resizeWindow");
      return false;
    }
    if (typeof width !== 'number' || typeof height !== 'number') {
      appLog("Invalid resize dimensions");
      return false;
    }
    appLog(`Resizing window to ${width} × ${height}`);
    const result = await window.electronAPI.resizeWindow(width, height);
    if (result) {
      windowSize.width = result.width;
      windowSize.height = result.height;
      if (window.ui && window.ui.updateSizeDisplay) {
        window.ui.updateSizeDisplay(windowSize.width, windowSize.height);
      }
      return true;
    }
    return false;
  } catch (error) {
    console.error("Error resizing window:", error);
    return false;
  }
}

async function saveResults() {
  const activeTab = document.querySelector('.tab.active');
  if (!activeTab) return;
  const tabId = activeTab.getAttribute('data-tab');
  let content;
  if (tabId === 'assistant') {
    const chatMessages = document.getElementById('chat-messages');
    if (!chatMessages) return;
    content = chatMessages.innerText;
  } else if (tabId === 'system-design') {
    const systemDesignMessages = document.getElementById('system-design-messages');
    if (!systemDesignMessages) return;
    content = systemDesignMessages.innerText;
  }
  if (!content || content.trim() === '') {
    alert('No content to save!');
    return;
  }
  try {
    if (window.electronAPI) {
      const saved = await window.electronAPI.saveToFile(content);
      const statusElement = document.getElementById('status');
      if (statusElement) statusElement.textContent = saved ? 'Results saved!' : 'Failed to save results';
    } else {
      const blob = new Blob([content], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${tabId}-chat-${new Date().toISOString().slice(0,19).replace(/:/g, '-')}.txt`;
      a.click();
      URL.revokeObjectURL(url);
      const statusElement = document.getElementById('status');
      if (statusElement) statusElement.textContent = 'Results downloaded!';
    }
  } catch (error) {
    console.error("Error saving results:", error);
    const statusElement = document.getElementById('status');
    if (statusElement) statusElement.textContent = 'Error saving results: ' + error.message;
  }
}

// Setup event listeners
function setupEventListeners() {
  appLog("Setting up event listeners");
  const elements = {
    closeBtn: document.getElementById('closeBtn'),
    chatInput: document.getElementById('chat-input'),
    sendButton: document.getElementById('send-button'),
    clearChat: document.getElementById('clear-chat'),
    systemDesignInput: document.getElementById('system-design-input'),
    systemDesignSendButton: document.getElementById('system-design-send-button'),
    systemDesignClearChat: document.getElementById('system-design-clear-chat'),
    startRecordingBtn: document.getElementById('startRecordingBtn'),
    stopRecordingBtn: document.getElementById('stopRecordingBtn'),
  };

  for (const [key, element] of Object.entries(elements)) {
    if (!element) appLog(`Error: #${key} not found in DOM`);
  }

  if (elements.closeBtn) {
    elements.closeBtn.addEventListener('click', async () => {
      try {
        if (window.electronAPI) {
          await window.electronAPI.closeWindow();
        } else {
          appLog("Error: window.electronAPI not available for closeWindow");
        }
      } catch (error) {
        console.error("Error closing window:", error);
      }
    });
  }

  if (elements.chatInput) {
    elements.chatInput.addEventListener('keypress', (event) => {
      if (event.key === 'Enter') sendAssistantMessage();
    });
  }

  if (elements.sendButton) {
    elements.sendButton.addEventListener('click', sendAssistantMessage);
  }

  if (elements.clearChat) {
    elements.clearChat.addEventListener('click', () => {
      if (window.ui && window.ui.clearChat) {
        window.ui.clearChat('chat-messages');
        assistantConversationHistory = [];
      } else {
        appLog("Error: window.ui.clearChat not available");
      }
    });
  }

  if (elements.systemDesignInput) {
    elements.systemDesignInput.addEventListener('keypress', (event) => {
      if (event.key === 'Enter') sendSystemDesignMessage();
    });
  }

  if (elements.systemDesignSendButton) {
    elements.systemDesignSendButton.addEventListener('click', sendSystemDesignMessage);
  }

  if (elements.systemDesignClearChat) {
    elements.systemDesignClearChat.addEventListener('click', () => {
      if (window.ui && window.ui.clearChat) {
        window.ui.clearChat('system-design-messages');
        systemDesignConversationHistory = [];
      } else {
        appLog("Error: window.ui.clearChat not available");
      }
    });
  }

  if (elements.startRecordingBtn) {
    elements.startRecordingBtn.addEventListener('click', async () => {
      appLog("Start recording button clicked");
      transcription = '';
      try {
        if (window.electronAPI) {
          const success = await window.electronAPI.startRecording();
          if (success) {
            appLog("Recording started successfully");
            toggleRecordingButtons(true);
          } else {
            appLog("Failed to start recording");
            const statusElement = document.getElementById('status');
            const recordingStatusElement = document.getElementById('recording-status');
            if (statusElement) statusElement.textContent = 'Failed to start recording';
            if (recordingStatusElement) recordingStatusElement.textContent = 'Failed';
          }
        } else {
          appLog("Error: window.electronAPI not available for startRecording");
        }
      } catch (error) {
        console.error("Error starting recording:", error);
        const statusElement = document.getElementById('status');
        const recordingStatusElement = document.getElementById('recording-status');
        if (statusElement) statusElement.textContent = 'Recording error: ' + error.message;
        if (recordingStatusElement) recordingStatusElement.textContent = 'Error';
      }
    });
  }

  if (elements.stopRecordingBtn) {
    elements.stopRecordingBtn.addEventListener('click', async () => {
      appLog("Stop recording button clicked");
      try {
        if (window.electronAPI) {
          const result = await window.electronAPI.stopRecording();
          if (result.success) {
            appLog("Recording stopped successfully");
            toggleRecordingButtons(false);
          } else {
            appLog("Failed to stop recording");
            const statusElement = document.getElementById('status');
            const recordingStatusElement = document.getElementById('recording-status');
            if (statusElement) statusElement.textContent = 'Failed to stop recording';
            if (recordingStatusElement) recordingStatusElement.textContent = 'Failed';
          }
        } else {
          appLog("Error: window.electronAPI not available for stopRecording");
        }
      } catch (error) {
        console.error("Error stopping recording:", error);
        const statusElement = document.getElementById('status');
        const recordingStatusElement = document.getElementById('recording-status');
        if (statusElement) statusElement.textContent = 'Recording error: ' + error.message;
        if (recordingStatusElement) recordingStatusElement.textContent = 'Error';
      }
    });
  }

  if (window.electronAPI) {
    appLog("Electron API available");
    window.electronAPI.onCapture(async () => {
      appLog("Capture event received");
      const loadingOverlay = document.getElementById('loadingOverlay');
      const statusElement = document.getElementById('status');
      if (loadingOverlay) loadingOverlay.style.display = 'flex';
      if (statusElement) statusElement.textContent = 'Capturing...';
      try {
        lastImageBuffer = await window.electronAPI.captureScreen();
        appLog("captureScreen returned", lastImageBuffer ? `${lastImageBuffer.length} bytes` : "null");
        if (statusElement) {
          statusElement.textContent = lastImageBuffer ? 'Captured! Ready to process.' : 'Capture failed';
        }
      } catch (error) {
        console.error("Error during capture:", error);
        if (statusElement) statusElement.textContent = 'Capture error: ' + error.message;
      } finally {
        if (loadingOverlay) loadingOverlay.style.display = 'none';
      }
    });

    window.electronAPI.onProcess(async () => {
      appLog("Process event received");
      if (!lastImageBuffer) {
        appLog("No image buffer available");
        const statusElement = document.getElementById('status');
        if (statusElement) statusElement.textContent = 'No image captured yet';
        if (window.ui && window.ui.addChatMessage) {
          window.ui.addChatMessage('chat-messages', `Please capture a screen first (${window.ui.isMac ? 'Cmd+Shift+C' : 'Ctrl+Shift+C'})`, false);
        }
        return;
      }
      const loadingOverlay = document.getElementById('loadingOverlay');
      const statusElement = document.getElementById('status');
      const activeTab = document.querySelector('.tab.active');
      if (!activeTab) return;
      const tabId = activeTab.getAttribute('data-tab');
      const containerId = tabId === 'assistant' ? 'chat-messages' : 'system-design-messages';
      const languageSelect = document.getElementById('language-select'); // Only available in Assistant tab
      const modelSelect = document.getElementById(tabId === 'assistant' ? 'model-select' : 'system-design-model-select');
      const language = tabId === 'assistant' ? (languageSelect ? languageSelect.value : null) : null;
      const model = modelSelect ? modelSelect.value : 'o3-mini'; // Default to o3-mini if not selected
      const conversationHistory = tabId === 'assistant' ? assistantConversationHistory : systemDesignConversationHistory;
      const isSystemDesign = tabId === 'system-design';
      const statusDisplay = document.getElementById(tabId === 'assistant' ? 'chat-status' : 'system-design-status');
      if (loadingOverlay) loadingOverlay.style.display = 'flex';
      if (statusElement) statusElement.textContent = 'Processing...';
      if (statusDisplay) statusDisplay.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
      try {
        const imageBase64 = lastImageBuffer.toString('base64');
        const imageDataUrl = `data:image/png;base64,${imageBase64}`;
        if (window.ui && window.ui.addChatImage) {
          window.ui.addChatImage(containerId, imageDataUrl, true);
        }

        // Step 1: Perform OCR using preload.js
        if (!window.electronAPI || !window.electronAPI.performOCR) {
          throw new Error('window.electronAPI.performOCR is not available');
        }
        const text = await window.electronAPI.performOCR(lastImageBuffer);
        appLog("OCR result:", text);
        if (text.startsWith('OCR error') || text === 'Missing credentials. Please sign in again.') {
          throw new Error(text);
        }

        // Step 2: Analyze with AI using preload.js
        if (!window.electronAPI || !window.electronAPI.analyzeWithAI) {
          throw new Error('window.electronAPI.analyzeWithAI is not available');
        }
        let prompt;
        if (isSystemDesign) {
          prompt = `
You are an expert in system design. Analyze the following screenshot content: "${text}". First, provide a diagram in Mermaid syntax to visualize the architecture (e.g., sequence diagram, component diagram, or architecture diagram). Use a code block with the "mermaid" language identifier. Then, provide a detailed system design explanation, including:
1. A step-by-step breakdown of the system architecture.
2. Key components and their interactions.
3. Scalability, reliability, and performance considerations.
`;
        } else {
          prompt = `Analyze this screenshot content: ${text}`;
        }
        conversationHistory.push({ role: 'user', content: `Screenshot analysis: ${text}` });
        limitHistory(conversationHistory); // Limit history (modifies in place)
        appLog(`Conversation history before analysis: ${JSON.stringify(conversationHistory)}`);

        const analysis = await window.electronAPI.analyzeWithAI(prompt, conversationHistory, model, language);
        appLog("AI analysis result:", analysis);
        if (analysis.startsWith('AI analysis error') || analysis === 'Missing credentials. Please sign in again.') {
          throw new Error(analysis);
        }

        conversationHistory.push({ role: 'assistant', content: analysis });
        limitHistory(conversationHistory); // Limit history (modifies in place)
        appLog(`Conversation history after analysis: ${JSON.stringify(conversationHistory)}`);

        if (window.ui && window.ui.addChatMessage) {
          window.ui.addChatMessage(containerId, analysis, false);
        }
        if (statusElement) statusElement.textContent = 'Done!';
        if (statusDisplay) statusDisplay.textContent = 'Ready';
      } catch (error) {
        console.error("Error during processing:", error);
        if (statusElement) statusElement.textContent = 'Processing error: ' + error.message;
        if (statusDisplay) statusDisplay.textContent = 'Error';
        if (window.ui && window.ui.addChatMessage) {
          window.ui.addChatMessage(containerId, 'Error: ' + error.message, false);
        }
      } finally {
        if (loadingOverlay) loadingOverlay.style.display = 'none';
      }
    });

    window.electronAPI.onRecordingStarted(() => {
      appLog("Recording started event received");
      toggleRecordingButtons(true);
    });

    window.electronAPI.onRecordingStopped(() => {
      appLog("Recording stopped event received");
      toggleRecordingButtons(false);
    });

    window.electronAPI.onAudioChunk((chunkBase64) => {
      appLog("Received audio chunk: " + chunkBase64);
    });

    window.electronAPI.on('transcription-result', (event, data) => {
      appLog("Received transcription result via IPC");
      transcription += (data.transcription || '') + ' ';
      if (window.ui && window.ui.addChatMessage) {
        window.ui.addChatMessage('chat-messages', `Transcription: ${data.transcription || 'N/A'}`, false);
      }
      if (data.response) {
        if (window.ui && window.ui.addChatMessage) {
          window.ui.addChatMessage('chat-messages', `AI Response: ${data.response}`, false);
        }
      }
      const recordingInfo = document.getElementById('recording-info');
      if (recordingInfo) {
        recordingInfo.textContent = `Transcription: ${transcription.trim()}\n\nLatest Response: ${data.response || 'N/A (<10 words)'}`;
      }
    });
  } else {
    appLog("Error: ElectronAPI not available, running in development mode");
  }

  document.addEventListener('keydown', (event) => {
    if (isModifierKey(event) && event.shiftKey && !event.altKey) {
      appLog(`Keydown: ${window.ui.isMac ? 'Cmd' : 'Ctrl'}+Shift+${event.key}`);
      switch (event.key) {
        case 'C':
          if (window.electronAPI) window.electronAPI.onCapture();
          event.preventDefault();
          break;
        case 'P':
          if (window.electronAPI) window.electronAPI.onProcess();
          event.preventDefault();
          break;
        case 'T':
          if (window.electronAPI) window.electronAPI.toggleWindow();
          event.preventDefault();
          break;
        case 'I':
          resizeWindow(windowSize.width, windowSize.height + resizeStep);
          event.preventDefault();
          break;
        case 'K':
          resizeWindow(windowSize.width, windowSize.height - resizeStep);
          event.preventDefault();
          break;
        case 'R':
          const startRecordingBtn = document.getElementById('startRecordingBtn');
          if (startRecordingBtn && startRecordingBtn.disabled) {
            if (window.electronAPI) window.electronAPI.stopRecording();
          } else {
            if (window.electronAPI) window.electronAPI.startRecording();
          }
          event.preventDefault();
          break;
        case 'ArrowUp':
          resizeWindow(windowSize.width, windowSize.height - resizeStep);
          event.preventDefault();
          break;
        case 'ArrowDown':
          resizeWindow(windowSize.width, windowSize.height + resizeStep);
          event.preventDefault();
          break;
        case 'ArrowLeft':
          resizeWindow(windowSize.width - resizeStep, windowSize.height);
          event.preventDefault();
          break;
        case 'ArrowRight':
          resizeWindow(windowSize.width + resizeStep, windowSize.height);
          event.preventDefault();
          break;
      }
    }
    if (isModifierKey(event) && !event.shiftKey && !event.altKey) {
      switch (event.key) {
        case 'ArrowUp':
          moveWindow(0, -moveStep);
          event.preventDefault();
          break;
        case 'ArrowDown':
          moveWindow(0, moveStep);
          event.preventDefault();
          break;
        case 'ArrowLeft':
          moveWindow(-moveStep, 0);
          event.preventDefault();
          break;
        case 'ArrowRight':
          moveWindow(moveStep, 0);
          event.preventDefault();
          break;
        case '+':
        case '=':
          resizeWindow(windowSize.width + zoomStep, windowSize.height + zoomStep);
          event.preventDefault();
          break;
        case '-':
          resizeWindow(windowSize.width - zoomStep, windowSize.height - zoomStep);
          event.preventDefault();
          break;
      }
    }
    if (isModifierKey(event) && event.altKey && !event.shiftKey) {
      switch (event.key) {
        case 'ArrowUp':
          scrollActiveTab('up');
          event.preventDefault();
          break;
        case 'ArrowDown':
          scrollActiveTab('down');
          event.preventDefault();
          break;
      }
    }
  });
}

// Initialize app
function initApp() {
  appLog("Initializing app");
  const credentials = JSON.parse(localStorage.getItem('helperCredentials'));
  if (credentials) appLog('Credentials retrieved in helper:', credentials);
  else appLog('No credentials found in localStorage');

  if (window.ui && window.ui.setupTabNavigation) {
    window.ui.setupTabNavigation();
  } else {
    appLog("Error: window.ui.setupTabNavigation not available");
  }

  // Populate model select options with only gpt-4o, deepseekR1, and o3-mini
  const models = ['gpt-4o', 'deepseekR1'];
  const modelSelect = document.getElementById('model-select');
  const systemDesignModelSelect = document.getElementById('system-design-model-select');
  if (modelSelect && systemDesignModelSelect) {
    models.forEach(model => {
      const option = document.createElement('option');
      option.value = model;
      option.textContent = model;
      modelSelect.appendChild(option.cloneNode(true));
      systemDesignModelSelect.appendChild(option.cloneNode(true));
    });
  } else {
    appLog("Error: model-select or system-design-model-select not found");
  }

  setupEventListeners();

  if (window.ui && window.ui.populateShortcutsHeader) {
    window.ui.populateShortcutsHeader();
  } else {
    appLog("Error: window.ui.populateShortcutsHeader not available");
  }

  if (window.ui && window.ui.populateShortcutsHelp) {
    window.ui.populateShortcutsHelp();
  } else {
    appLog("Error: window.ui.populateShortcutsHelp not available");
  }
}

// Initialize after DOM is fully loaded
document.addEventListener('DOMContentLoaded', async () => {
  appLog("App script loaded and DOM is ready");

  if (typeof window.electronAPI === 'undefined') {
    appLog("Error: window.electronAPI is not defined. Check preload.js and main.js");
  } else {
    appLog("window.electronAPI is available");
  }

  initApp();

  if (window.electronAPI) {
    try {
      const position = await window.electronAPI.getWindowPosition();
      if (position && typeof position.x === 'number' && typeof position.y === 'number') {
        windowPosition = position;
      }
      const size = await window.electronAPI.getWindowSize();
      if (size && typeof size.width === 'number' && typeof size.height === 'number') {
        windowSize = size;
        if (window.ui && window.ui.updateSizeDisplay) {
          window.ui.updateSizeDisplay(windowSize.width, windowSize.height);
        }
      }
    } catch (error) {
      console.error("Error initializing window properties:", error);
    }
  }
});