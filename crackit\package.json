{"name": "interviewcracker-tool", "version": "1.0.0", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder --win --config win-build.json", "build:mac": "electron-builder --mac"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "description": "", "devDependencies": {"electron": "^35.0.3", "electron-builder": "^25.1.8"}, "build": {"appId": "in.interviewcracker.crackit", "productName": "InterviewCracker", "artifactName": "interviewCracker.${ext}", "directories": {"output": "dist"}, "files": ["**/*", "!dist/", "!node_modules/.cache"], "mac": {"target": "dmg", "icon": "icon.png"}, "win": {"target": "nsis", "icon": "icon.png"}}, "dependencies": {"child_process": "^1.0.2", "dotenv": "^16.4.7", "node-fetch": "^2.7.0", "socket.io-client": "^4.8.1"}}