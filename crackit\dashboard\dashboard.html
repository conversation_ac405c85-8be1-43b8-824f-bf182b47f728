<!DOCTYPE html>
<html>
<head>
  <title>Crack it Dashboard</title>
  <link rel="stylesheet" href="styles2.css">
  <style>
  
  </style>
</head>
<body>
  <div class="layout" id="dashboardLayout">
    <header class="header">
      <div class="logo-container">
        <img src="../icon.png" alt="Logo" class="logo">
        <div class="logo-text">InterviewCracker.in</div>
      </div>
    </header>
    
    <div class="content-wrapper">
      <aside class="shortcuts-sidebar hidden">
        <h3 class="shortcuts-title">Keyboard Shortcuts</h3>
        <div class="shortcut-category">Dashboard Controls</div>
        <div class="shortcut-item">
          <div class="shortcut-key">Ctrl+Shift+D</div>
          <div class="shortcut-description">Show Dashboard</div>
        </div>
        <div class="shortcut-item">
          <div class="shortcut-key">Ctrl+Shift+M</div>
          <div class="shortcut-description">Minimize Dashboard</div>
        </div>
        <div class="shortcut-category">Helper Controls</div>
        <div class="shortcut-item">
          <div class="shortcut-key">Ctrl+Shift+S</div>
          <div class="shortcut-description">Show Helper</div>
        </div>
        <div class="shortcut-item">
          <div class="shortcut-key">Ctrl+Shift+H</div>
          <div class="shortcut-description">Hide Helper</div>
        </div>
        <div class="shortcut-item">
          <div class="shortcut-key">Ctrl+Shift+T</div>
          <div class="shortcut-description">Toggle Helper Visibility</div>
        </div>
        <div class="Note"> Note - If launch helper does not launch while pressing the launch button , Press Ctrl+Shift+T to toggle </div>
      </aside>
      
      <main class="main-content">
        <div class="container">
          <div id="signin-section">
            <div class="card-header">
              <h2>Sign in to your account</h2>
            </div>
            <div class="card-body">
              <div class="form-group">
                <label class="form-label" for="email">Email</label>
                <input type="text" id="email" placeholder="Enter your email" />
                <div id="emailError" class="form-error hidden"></div>
              </div>
              <div class="form-group">
                <label class="form-label" for="secretKey">Secret Key</label>
                <input type="password" id="secretKey" placeholder="Enter your secret key" />
                <div id="secretKeyError" class="form-error hidden"></div>
              </div>
              <div id="authError" class="form-error hidden"></div>
              <button id="signinBtn">Sign In</button>
              <div class="signup-link">
                Don't have an account? <a href="#" id="signupLink">Sign up</a>
              </div>
            </div>
          </div>
          
          <div id="info-section" class="hidden">
            <div class="card-header">
              <h2>Dashboard</h2>
              <div id="welcomeMessage" class="welcome-message"></div>
            </div>
            <div class="card-body">
              <div class="info-grid">
                <div class="info-card">
                  <div class="info-header">Max Prompts</div>
                  <div class="max-prompts">5</div>
                </div>
                <div class="info-card">
                  <div class="info-header">Used Prompts</div>
                  <div class="prompts-used">0</div>
                </div>
                <div class="info-card">
                  <div class="info-header">Your Plan</div>
                  <div>
                    <span id="planBadge" class="plan-badge">Free</span>
                  </div>
                </div>
              </div>
              <div id="timerSection" class="timer-section hidden">
                <div class="timer-title">Account Status</div>
                <div class="timer-display">
                  <span class="timer-label">Subscription ends in:</span>
                  <span id="subscriptionTimer" class="timer-value">--</span>
                </div>
                <div class="timer-display">
                  <span class="timer-label">Prompts reset in:</span>
                  <span id="promptResetTimer" class="timer-value">--</span>
                </div>
              </div>
              <button id="launchHelperBtn" class="action-button">Launch Helper</button>
              <div id="limitMessage" class="limit-message hidden"></div>
              <button id="logoutBtn" class="secondary-button">Log Out</button>
            </div>
          </div>
        </div>
      </main>
    </div>
    
    <footer class="footer">
      <p>© 2025 InterviewCracker.in. All rights reserved.</p>
    </footer>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', async () => {
      const APP_VERSION = '1.0.0';
      const SERVER_URL = 'https://server-763917579770.us-central1.run.app';
      const DOWNLOAD_URL = 'https://interviewcracker.in/dashboard';

      const signinBtn = document.getElementById('signinBtn');
      const signupLink = document.getElementById('signupLink');
      const logoutBtn = document.getElementById('logoutBtn');
      const launchHelperBtn = document.getElementById('launchHelperBtn');
      const signinSection = document.getElementById('signin-section');
      const infoSection = document.getElementById('info-section');
      const limitMessage = document.getElementById('limitMessage');
      const welcomeMessage = document.getElementById('welcomeMessage');
      const subscriptionTimer = document.getElementById('subscriptionTimer');
      const promptResetTimer = document.getElementById('promptResetTimer');
      const timerSection = document.getElementById('timerSection');
      const planBadge = document.getElementById('planBadge');
      const shortcutsSidebar = document.querySelector('.shortcuts-sidebar');

      let subscriptionTimerInterval = null;
      let promptResetTimerInterval = null;

      async function checkForUpdates() {
        try {
          const response = await fetch(`${SERVER_URL}/check-update?version=${APP_VERSION}`);
          const data = await response.json();
          if (data.isUpdateAvailable) {
            const message = `${data.message}\nCurrent Version: ${data.clientVersion}\nLatest Version: ${data.latestVersion}\n\nDownload the latest version here: ${DOWNLOAD_URL}\n\nClick OK to close the app.`;
            if (window.electronAPI && window.electronAPI.showMessageBox) {
              await window.electronAPI.showMessageBox({
                type: 'warning',
                title: 'Update Required',
                message: message,
                buttons: ['OK'],
                defaultId: 0,
                cancelId: 0
              });
              window.electronAPI.openExternalLink(DOWNLOAD_URL);
              window.electronAPI.quitApp();
            } else {
              alert(message);
              window.open(DOWNLOAD_URL, '_blank');
              return false;
            }
            return false;
          } else {
            console.log(data.message);
            return true;
          }
        } catch (error) {
          console.error('Failed to check for updates:', error);
          alert('Failed to verify app version. Please check your internet connection and try again.');
          return false;
        }
      }

      const canProceed = await checkForUpdates();
      if (!canProceed) return;

      const user = JSON.parse(localStorage.getItem('user'));
      if (user) {
        showDashboard(user);
        refreshUserDetails();
      }

      signupLink.addEventListener('click', (event) => {
        event.preventDefault();
        if (window.electronAPI && window.electronAPI.openExternalLink) {
          window.electronAPI.openExternalLink('https://interviewcracker.in/register');
        } else {
          window.open('https://interviewcracker.in/register', '_blank');
        }
      });

      signinBtn.addEventListener('click', async () => {
        const email = document.getElementById('email').value.trim();
        const secretKey = document.getElementById('secretKey').value.trim();
        
        document.getElementById('emailError').classList.add('hidden');
        document.getElementById('secretKeyError').classList.add('hidden');
        document.getElementById('authError').classList.add('hidden');
        
        let hasError = false;
        if (!email) {
          document.getElementById('emailError').textContent = 'Email is required';
          document.getElementById('emailError').classList.remove('hidden');
          hasError = true;
        }
        if (!secretKey) {
          document.getElementById('secretKeyError').textContent = 'Secret key is required';
          document.getElementById('secretKeyError').classList.remove('hidden');
          hasError = true;
        }
        if (hasError) return;

        try {
          const response = await fetch(`${SERVER_URL}/verify-credentials`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email, secretKey, ip: await getUserIP() }),
          });
          const data = await response.json();
          if (response.ok) {
            const userWithSecret = { ...data.user, secretKey };
            localStorage.setItem('user', JSON.stringify(userWithSecret));
            showDashboard(userWithSecret);
            await refreshUserDetails();
          } else {
            document.getElementById('authError').textContent = data.error || 'Invalid credentials';
            document.getElementById('authError').classList.remove('hidden');
          }
        } catch (error) {
          console.error('Sign-in error:', error);
          document.getElementById('authError').textContent = 'Sign-in failed. Please try again.';
          document.getElementById('authError').classList.remove('hidden');
        }
      });

      launchHelperBtn.addEventListener('click', async () => {
        const user = JSON.parse(localStorage.getItem('user'));
        if (!user) {
          alert('Please sign in first');
          return;
        }

        if (user.promptsUsed >= user.maxPrompts) {
          handlePromptLimitExceeded(user);
          return;
        }

        localStorage.setItem('helperCredentials', JSON.stringify({
          email: user.email,
          secretKey: user.secretKey
        }));

        if (window.electronAPI && window.electronAPI.launchHelper) {
          const launched = await window.electronAPI.launchHelper();
          if (!launched) {
            limitMessage.textContent = 'Helper is already open. Please close the existing helper window.';
            limitMessage.classList.remove('hidden');
            setTimeout(() => limitMessage.classList.add('hidden'), 5000);
          } else {
            limitMessage.classList.add('hidden');
          }
        } else {
          limitMessage.textContent = 'Helper launch failed. Please ensure the application is running in the correct environment.';
          limitMessage.classList.remove('hidden');
          setTimeout(() => limitMessage.classList.add('hidden'), 5000);
        }
      });

      logoutBtn.addEventListener('click', () => {
        localStorage.removeItem('user');
        localStorage.removeItem('helperCredentials');
        infoSection.classList.add('hidden');
        signinSection.classList.remove('hidden');
        shortcutsSidebar.classList.add('hidden');
        limitMessage.classList.add('hidden');
        launchHelperBtn.classList.remove('hidden');
        timerSection.classList.add('hidden');
        if (subscriptionTimerInterval) clearInterval(subscriptionTimerInterval);
        if (promptResetTimerInterval) clearInterval(promptResetTimerInterval);
        document.getElementById('email').value = '';
        document.getElementById('secretKey').value = '';
      });

      function showDashboard(user) {
        signinSection.classList.add('hidden');
        infoSection.classList.remove('hidden');
        shortcutsSidebar.classList.remove('hidden');
        updateDashboardUI(user);
      }

      function updateDashboardUI(user) {
        welcomeMessage.textContent = `Welcome, ${user.name || 'User'}`;
        document.querySelector('.max-prompts').textContent = user.maxPrompts || 5;
        document.querySelector('.prompts-used').textContent = user.promptsUsed || 0;

        if (user.subscriptionType === 'premium') {
          planBadge.textContent = 'Premium';
          planBadge.classList.add('premium');
        } else {
          planBadge.textContent = 'Free';
          planBadge.classList.remove('premium');
        }

        if (user.subscriptionType === 'premium' && user.subscriptionEndDate) {
          timerSection.classList.remove('hidden');
          startSubscriptionTimer(user.subscriptionEndDate);
          startPromptResetTimer(user.lastResetDate);
        } else {
          timerSection.classList.add('hidden');
          if (subscriptionTimerInterval) clearInterval(subscriptionTimerInterval);
          if (promptResetTimerInterval) clearInterval(promptResetTimerInterval);
        }

        if (user.promptsUsed >= user.maxPrompts) {
          handlePromptLimitExceeded(user);
        } else {
          limitMessage.classList.add('hidden');
          launchHelperBtn.disabled = false;
          launchHelperBtn.classList.remove('hidden');
        }
      }

      function handlePromptLimitExceeded(user) {
        if (window.electronAPI && window.electronAPI.closeWindow) {
          window.electronAPI.closeWindow();
        }
        let message = `Maximum number of prompts (${user.maxPrompts}) reached for ${user.subscriptionType === 'premium' ? 'Premium' : 'Free'} plan.`;
        if (user.subscriptionType === 'premium' && user.lastResetDate) {
          const promptResetTimeRemaining = calculatePromptResetTimeRemaining(user.lastResetDate);
          message += ` Prompts will reset in ${promptResetTimeRemaining}.`;
        }
        limitMessage.textContent = message;
        limitMessage.classList.remove('hidden');
      }

      function calculateTimeRemaining(subscriptionEndDate) {
        const endDate = new Date(subscriptionEndDate);
        const now = new Date();
        const timeDiff = endDate - now;
        if (timeDiff <= 0) return "Expired";
        const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);
        let timeString = '';
        if (days > 0) timeString += `${days}d `;
        if (hours > 0 || days > 0) timeString += `${hours}h `;
        if (minutes > 0 || hours > 0 || days > 0) timeString += `${minutes}m `;
        timeString += `${seconds}s`;
        return timeString.trim();
      }

      function calculatePromptResetTimeRemaining(lastResetDate) {
        const lastReset = new Date(lastResetDate);
        const oneWeekInMs = 7 * 24 * 60 * 60 * 1000;
        const nextReset = new Date(lastReset.getTime() + oneWeekInMs);
        const now = new Date();
        const timeDiff = nextReset - now;
        if (timeDiff <= 0) return "Due now";
        const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);
        let timeString = '';
        if (days > 0) timeString += `${days}d `;
        if (hours > 0 || days > 0) timeString += `${hours}h `;
        if (minutes > 0 || hours > 0 || days > 0) timeString += `${minutes}m `;
        timeString += `${seconds}s`;
        return timeString.trim();
      }

      function startSubscriptionTimer(subscriptionEndDate) {
        if (subscriptionTimerInterval) clearInterval(subscriptionTimerInterval);
        const updateSubscriptionTimer = () => {
          const timeRemaining = calculateTimeRemaining(subscriptionEndDate);
          subscriptionTimer.textContent = timeRemaining;
          if (timeRemaining === "Expired") refreshUserDetails();
        };
        updateSubscriptionTimer();
        subscriptionTimerInterval = setInterval(updateSubscriptionTimer, 1000);
      }

      function startPromptResetTimer(lastResetDate) {
        if (promptResetTimerInterval) clearInterval(promptResetTimerInterval);
        const updatePromptResetTimer = () => {
          const timeRemaining = calculatePromptResetTimeRemaining(lastResetDate);
          promptResetTimer.textContent = timeRemaining;
          if (timeRemaining === "Due now") refreshUserDetails();
        };
        updatePromptResetTimer();
        promptResetTimerInterval = setInterval(updatePromptResetTimer, 1000);
      }

      async function refreshUserDetails() {
        const user = JSON.parse(localStorage.getItem('user'));
        if (!user || !user.email || !user.secretKey) return;
        try {
          const response = await fetch(`${SERVER_URL}/user-details?email=${encodeURIComponent(user.email)}`, {
            method: 'GET',
            headers: { 'x-secret-key': user.secretKey }
          });
          const data = await response.json();
          if (response.ok) {
            const updatedUser = { ...user, ...data.user };
            localStorage.setItem('user', JSON.stringify(updatedUser));
            updateDashboardUI(updatedUser);
          } else if (response.status === 403) {
            handlePromptLimitExceeded(user);
          } else {
            console.error('Failed to fetch user details:', data.error || 'Unknown error');
          }
        } catch (error) {
          console.error('Failed to refresh user details:', error);
        }
      }
      
      setInterval(() => {
        if (!infoSection.classList.contains('hidden')) {
          refreshUserDetails();
        }
      }, 30000);

      async function getUserIP() {
        try {
          const response = await fetch('https://api.ipify.org?format=json');
          const data = await response.json();
          return data.ip;
        } catch (error) {
          console.error('Failed to get IP:', error);
          return 'unknown';
        }
      }
    });
  </script>
</body>
</html>