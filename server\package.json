{"name": "server", "version": "1.0.0", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "description": "", "dependencies": {"axios": "^1.8.4", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "fluent-ffmpeg": "^2.1.3", "google-auth-library": "^9.15.1", "mongoose": "^8.12.1", "multer": "^1.4.5-lts.1", "openai": "^4.86.2", "socket.io": "^4.8.1", "tesseract.js": "^6.0.0"}}