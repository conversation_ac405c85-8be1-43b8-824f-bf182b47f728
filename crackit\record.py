import soundcard as sc
import numpy as np
import sys
import time
import signal
import threading
import base64
from scipy.signal import resample
import wave
import io
from queue import Queue

# Configuration
ORIGINAL_SAMPLE_RATE = 44100
TARGET_SAMPLE_RATE = 16000
CHUNK_SIZE = ORIGINAL_SAMPLE_RATE * 1  # 1 second of audio
USE_WAV = True
AMPLITUDE_THRESHOLD = 500

# Flags and queue
recording = False
should_exit = False
audio_queue = Queue()

def signal_handler(sig, frame):
    global should_exit
    should_exit = True
    print("Signal received, preparing to exit...")
    sys.stdout.flush()

signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

def record_audio():
    global recording, should_exit

    try:
        print("Initializing recorders...")
        sys.stdout.flush()

        mic_recorder = sc.default_microphone().recorder(samplerate=ORIGINAL_SAMPLE_RATE)
        loopback_recorder = None
        try:
            loopback_recorder = sc.get_microphone(
                id=str(sc.default_speaker().name), include_loopback=True
            ).recorder(samplerate=ORIGINAL_SAMPLE_RATE)
        except Exception as e:
            print(f"Loopback error: {e}. Proceeding with microphone only.")
            sys.stdout.flush()

        with mic_recorder, (loopback_recorder if loopback_recorder else mic_recorder):
            print("Recorders ready")
            sys.stdout.flush()

            while not should_exit:
                if recording:
                    mic_chunk = mic_recorder.record(numframes=CHUNK_SIZE)
                    loopback_chunk = (
                        loopback_recorder.record(numframes=CHUNK_SIZE)
                        if loopback_recorder
                        else np.zeros_like(mic_chunk)
                    )
                    min_length = min(len(mic_chunk), len(loopback_chunk))
                    mixed_chunk = (mic_chunk[:min_length, 0] + loopback_chunk[:min_length, 0]) / 2
                    audio_queue.put(mixed_chunk)
                else:
                    time.sleep(0.01)
    except Exception as e:
        print(f"Error in recording: {str(e)}")
        sys.stdout.flush()

def process_audio():
    while not should_exit:
        if not audio_queue.empty():
            mixed_chunk = audio_queue.get()
            resampled_length = int(len(mixed_chunk) * TARGET_SAMPLE_RATE / ORIGINAL_SAMPLE_RATE)
            resampled_audio = resample(mixed_chunk, resampled_length)
            audio_int16 = np.int16(np.clip(resampled_audio * 32767, -32768, 32767))

            peak_amplitude = np.max(np.abs(audio_int16))
            print(f"Audio chunk stats: min={audio_int16.min()}, max={audio_int16.max()}, mean={audio_int16.mean():.2f}, peak={peak_amplitude}")
            sys.stdout.flush()

            if peak_amplitude < AMPLITUDE_THRESHOLD:
                print("Chunk skipped: amplitude below threshold")
                sys.stdout.flush()
                continue

            wav_buffer = io.BytesIO()
            with wave.open(wav_buffer, 'wb') as wav_file:
                wav_file.setnchannels(1)
                wav_file.setsampwidth(2)
                wav_file.setframerate(TARGET_SAMPLE_RATE)
                wav_file.writeframes(audio_int16.tobytes())
            audio_bytes = wav_buffer.getvalue()

            chunk_base64 = base64.b64encode(audio_bytes).decode('utf-8')
            sys.stdout.write(chunk_base64 + '\n')
            sys.stdout.flush()
        else:
            time.sleep(0.01)

def process_commands():
    global recording, should_exit

    print("Command processor ready. Send 'y' to start, 'n' to stop, 'q' to quit.")
    sys.stdout.flush()

    while not should_exit:
        command = sys.stdin.readline().strip().lower()
        if not command:
            time.sleep(0.1)
            continue

        print(f"Command received: {command}")
        sys.stdout.flush()

        if command == 'y':
            if not recording:
                recording = True
                print("Recording started")
            else:
                print("Already recording")
        elif command == 'n':
            if recording:
                recording = False
                print("Recording stopped")
            else:
                print("Not currently recording")
        elif command == 'q':
            should_exit = True
            print("Exiting")
        sys.stdout.flush()

if __name__ == "__main__":
    print("Audio recorder starting...")
    sys.stdout.flush()

    record_thread = threading.Thread(target=record_audio, daemon=True)
    process_thread = threading.Thread(target=process_audio, daemon=True)
    command_thread = threading.Thread(target=process_commands, daemon=True)

    record_thread.start()
    process_thread.start()
    command_thread.start()

    try:
        while not should_exit and (record_thread.is_alive() or process_thread.is_alive() or command_thread.is_alive()):
            time.sleep(0.1)
    except KeyboardInterrupt:
        print("Interrupted by user")
        should_exit = True
    finally:
        recording = False
        should_exit = True
        print("Waiting for threads to finish...")
        sys.stdout.flush()
        record_thread.join(timeout=2)
        process_thread.join(timeout=2)
        command_thread.join(timeout=2)
        print("Recorder exited")
        sys.stdout.flush()