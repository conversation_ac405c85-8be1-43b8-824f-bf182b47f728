const express = require('express');
const { createWorker } = require('tesseract.js');
const OpenAI = require('openai');
const dotenv = require('dotenv');
const multer = require('multer');
const fs = require('fs');
const path = require('path');
const cors = require('cors');
const mongoose = require('mongoose');
const rateLimit = require('express-rate-limit');
const { body, param, validationResult } = require('express-validator');
const http = require('http');
const { Server } = require('socket.io');
const axios = require('axios');

dotenv.config();

const app = express();
app.set('trust proxy', 1); // For Cloud Run
app.use(cors());

const port = process.env.PORT || 8080;

// Rate Limiting Middleware
app.use(rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // 100 requests per IP
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
}));

// MongoDB Connection
mongoose.connect(process.env.MONGODB_URI, {
  serverSelectionTimeoutMS: 120000,
  socketTimeoutMS: 150000,
  connectTimeoutMS: 120000,
  heartbeatFrequencyMS: 30000,
  maxPoolSize: 10,
  minPoolSize: 3,
  retryWrites: true,
  retryReads: true,
  w: 'majority',
})
  .then(async () => {
    console.log('MongoDB connected successfully');
    try {
      const result = await User.updateMany(
        { lastResetDate: { $exists: false } },
        { $set: { lastResetDate: new Date() } }
      );
      console.log(`Migration: Updated ${result.modifiedCount} user documents with lastResetDate`);
    } catch (error) {
      console.error('Migration error:', error);
    }
  })
  .catch(err => console.error('MongoDB connection error:', err));

// Connection error handlers
mongoose.connection.on('error', (err) => {
  console.error('MongoDB connection error:', err);
  mongoose.disconnect();
  setTimeout(() => mongoose.connect(process.env.MONGODB_URI).catch(err => console.error('Reconnection failed:', err)), 5000);
});

mongoose.connection.on('disconnected', () => {
  console.warn('MongoDB disconnected. Attempting to reconnect...');
  setTimeout(() => mongoose.connect(process.env.MONGODB_URI).catch(err => console.error('Reconnection failed:', err)), 5000);
});

// User Schema
const userSchema = new mongoose.Schema({
  name: String,
  email: { type: String, required: true, unique: true },
  secretKey: { type: String, required: true },
  subscriptionType: { type: String, default: 'free' },
  subscriptionEndDate: { type: Date, default: null },
  remainingTries: { type: Number, default: 3 },
  promptsUsed: { type: Number, default: 0 },
  createdAt: { type: Date, default: Date.now },
  lastResetDate: { type: Date, default: Date.now }
});
const User = mongoose.model('User', userSchema);

// Multer Storage Configuration
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const dir = path.join(__dirname, 'uploads');
    if (!fs.existsSync(dir)) fs.mkdirSync(dir, { recursive: true });
    cb(null, dir);
  },
  filename: (req, file, cb) => {
    cb(null, `${file.fieldname}-${Date.now()}${path.extname(file.originalname)}`);
  }
});
const upload = multer({ storage });

app.use(express.json({ limit: '50mb' }));
app.use(express.raw({ type: 'image/png', limit: '50mb' }));

const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

// Model API Functions
async function openAIModel(prompt, conversation, language, model) {
  const messages = [
    { role: 'system', content: `You are Grok 3 by xAI. Date: March 25, 2025. If the user requests code, generate it in ${language || 'any language'}.` },
    ...(conversation || []),
    { role: 'user', content: prompt }
  ];
  try {
    const response = await openai.chat.completions.create({
      model: model, // 'gpt-4o' or 'gpt-4o-mini'
      messages: messages,
      temperature: 0.7,
      max_tokens: 2048,
    });
    return response.choices[0].message.content;
  } catch (error) {
    console.error('OpenAI API Error:', error);
    throw new Error(`OpenAI API Error: ${error.message}`);
  }
}

async function deepseekModel(prompt, conversation, language) {
  try {
    const response = await axios.post('https://api.deepseek.com/chat/completions', {
      model: 'deepseek-chat',
      messages: [
        { role: 'system', content: `You are Grok 3 by xAI. Date: March 25, 2025. If the user requests code, generate it in ${language || 'any language'}.` },
        ...(conversation || []),
        { role: 'user', content: prompt }
      ],
      temperature: 0.7,
      max_tokens: 2048
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000 // 30 seconds timeout
    });
    return response.data.choices[0].message.content;
  } catch (error) {
    console.error('DeepSeek API Error:', error);
    throw new Error(`DeepSeek API Error: ${error.message}`);
  }
}

// Helper Function to Route AI Requests
async function getAIExplanation(prompt, conversation, model, language) {
  switch (model) {
    case 'gpt-4o':
      return await openAIModel(prompt, conversation, language, 'gpt-4o');
    case 'deepseekR1':
      return await deepseekModel(prompt, conversation, language);
    case 'o3-mini': // Assuming this is 'gpt-4o-mini'
      return await openAIModel(prompt, conversation, language, 'gpt-4o-mini');
    default:
      throw new Error(`Unsupported model: ${model}`);
  }
}

// Tesseract Worker Pool
const workerPool = [];
const maxWorkers = 2;

async function initializeWorkers() {
  for (let i = 0; i < maxWorkers; i++) {
    try {
      const worker = await createWorker('eng');
      workerPool.push(worker);
      console.log(`Initialized worker ${i + 1}`);
    } catch (error) {
      console.error(`Failed to initialize worker ${i + 1}:`, error);
    }
  }
}

(async () => {
  try {
    await initializeWorkers();
  } catch (error) {
    console.error('Error initializing Tesseract workers:', error);
  }
})();

let workerIndex = 0;
function getWorker() {
  if (workerPool.length === 0) return null;
  const worker = workerPool[workerIndex];
  workerIndex = (workerIndex + 1) % workerPool.length;
  return worker;
}

// Logging Functionality
const LOG_FILE = path.join(__dirname, 'transcriptions.log');

function logTranscription(transcribedText, explanation) {
  const now = new Date().toISOString();
  const logEntry = `[${now}] Transcription: ${transcribedText}\n[${now}] Explanation: ${explanation || 'N/A'}\n\n`;
  fs.appendFile(LOG_FILE, logEntry, (err) => {
    if (err) console.error('Error writing to log file:', err);
  });
  console.log(logEntry);
}

// User Verification Middleware
async function verifyUserAndPromptLimit(req, res, next) {
  const email = req.query.email || req.body.email;
  const secretKey = req.query.secretKey || req.body.secretKey || req.headers['x-secret-key'];

  if (!email || !secretKey) {
    return res.status(400).json({ error: 'Email and secret key are required' });
  }

  try {
    let user = await User.findOne({ email }).maxTimeMS(60000);
    if (!user) return res.status(404).json({ error: 'User not found' });
    if (user.secretKey !== secretKey) return res.status(401).json({ error: 'Invalid secret key' });

    user = await updateSubscriptionStatus(user);
    user = await resetPromptsIfNeeded(user);

    const maxPrompts = user.subscriptionType === 'premium' ? 250 : 5;
    if (user.promptsUsed >= maxPrompts) {
      return res.status(403).json({
        error: `Maximum number of prompts (${maxPrompts}) reached for ${user.subscriptionType === 'premium' ? 'Premium' : 'Free'} subscription.`
      });
    }

    req.user = user;
    req.maxPrompts = maxPrompts;
    next();
  } catch (error) {
    console.error('User verification error:', error);
    res.status(500).json({ error: 'Internal server error during user verification' });
  }
}

async function verifyUser(req, res, next) {
  const email = req.query.email || req.body.email;
  const secretKey = req.query.secretKey || req.body.secretKey || req.headers['x-secret-key'];

  if (!email || !secretKey) {
    return res.status(400).json({ error: 'Email and secret key are required' });
  }

  try {
    let user = await User.findOne({ email }).maxTimeMS(60000);
    if (!user) return res.status(404).json({ error: 'User not found' });
    if (user.secretKey !== secretKey) return res.status(401).json({ error: 'Invalid secret key' });

    user = await updateSubscriptionStatus(user);
    user = await resetPromptsIfNeeded(user);

    req.user = user;
    next();
  } catch (error) {
    console.error('User verification error:', error);
    res.status(500).json({ error: 'Internal server error during user verification' });
  }
}

// Register Endpoint
app.post('/register', [
  body('name').trim().notEmpty().withMessage('Name is required'),
  body('email').isEmail().normalizeEmail({ gmail_remove_dots: false, all_lowercase: true }).withMessage('Valid email is required'),
  body('secretKey').trim().notEmpty().withMessage('Secret key is required'),
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) return res.status(400).json({ error: errors.array()[0].msg });

  const { name, email, secretKey } = req.body;
  try {
    const existingUser = await User.findOne({ email }).maxTimeMS(60000);
    if (existingUser) return res.status(400).json({ error: 'Email already exists' });

    const user = new User({ name, email, secretKey, subscriptionType: 'free', subscriptionEndDate: null, remainingTries: 3, promptsUsed: 0, lastResetDate: new Date() });
    await user.save({ timeout: 60000 });

    res.json({
      user: { name, email, secretKey, subscriptionType: user.subscriptionType, subscriptionEndDate: user.subscriptionEndDate, remainingTries: user.remainingTries, promptsUsed: user.promptsUsed, lastResetDate: user.lastResetDate }
    });
  } catch (error) {
    console.error('Register error:', error);
    res.status(500).json({ error: 'Registration failed' });
  }
});

// Verify Credentials Endpoint
app.post('/verify-credentials', [
  body('email').isEmail().normalizeEmail({ gmail_remove_dots: false, all_lowercase: true }).withMessage('Valid email is required'),
  body('secretKey').trim().notEmpty().withMessage('Secret key is required'),
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) return res.status(400).json({ error: errors.array()[0].msg });

  const { email, secretKey } = req.body;

  let retryCount = 0;
  const maxRetries = 3;

  async function attemptVerification() {
    try {
      let user = await User.findOne({ email }).maxTimeMS(60000);
      if (!user) return res.status(400).json({ error: 'User not found' });
      if (user.secretKey !== secretKey) return res.status(401).json({ error: 'Invalid secret key' });

      user = await updateSubscriptionStatus(user);
      user = await resetPromptsIfNeeded(user);

      return res.json({
        user: { name: user.name, email: user.email, secretKey: user.secretKey, subscriptionType: user.subscriptionType, subscriptionEndDate: user.subscriptionEndDate, remainingTries: user.remainingTries, promptsUsed: user.promptsUsed, lastResetDate: user.lastResetDate }
      });
    } catch (error) {
      console.error(`Verification attempt ${retryCount + 1} failed:`, error);
      if (retryCount < maxRetries && (error.name === 'MongooseError' || error.name === 'MongoTimeoutError' || error.message.includes('timed out'))) {
        retryCount++;
        console.log(`Retrying verification (attempt ${retryCount} of ${maxRetries})...`);
        setTimeout(attemptVerification, retryCount * 1000);
      } else {
        return res.status(500).json({ error: 'Authentication failed', details: 'Database connection issue. Please try again in a moment.' });
      }
    }
  }

  await attemptVerification();
});

// User Details Endpoint
app.get('/user-details', verifyUser, async (req, res) => {
  try {
    const email = req.user.email;
    let user = await User.findOne({ email }).maxTimeMS(60000);
    if (!user) return res.status(404).json({ error: 'User not found' });

    if (!user.lastResetDate) {
      user.lastResetDate = new Date();
      await user.save({ timeout: 30000 });
      console.log(`Set lastResetDate for ${user.email}`);
    }
    user = await updateSubscriptionStatus(user);
    user = await resetPromptsIfNeeded(user);
    const maxPrompts = user.subscriptionType === 'premium' ? 250 : 5;
    res.json({
      user: { name: user.name, email: user.email, secretKey: user.secretKey, subscriptionType: user.subscriptionType, subscriptionEndDate: user.subscriptionEndDate, remainingTries: user.remainingTries, promptsUsed: user.promptsUsed, lastResetDate: user.lastResetDate, maxPrompts }
    });
  } catch (error) {
    console.error('User details error:', error);
    res.status(500).json({ error: 'Failed to fetch user details' });
  }
});

// OCR Endpoint
app.post('/ocr', verifyUserAndPromptLimit, upload.single('image'), async (req, res) => {
  let filePath = null;
  try {
    if (req.file) {
      filePath = req.file.path;
    } else if (req.body && req.body.length) {
      const tempDir = path.join(__dirname, 'uploads');
      if (!fs.existsSync(tempDir)) fs.mkdirSync(tempDir, { recursive: true });
      filePath = path.join(tempDir, `image-${Date.now()}.png`);
      fs.writeFileSync(filePath, req.body);
    } else {
      return res.status(400).json({ error: 'No image provided' });
    }

    const worker = getWorker();
    if (!worker) return res.status(503).json({ error: 'OCR service not ready yet. Please try again in a moment.' });

    const { data } = await worker.recognize(filePath);
    console.log('OCR extracted text length:', data.text.length);

    const updatedUser = await incrementPromptCount(req.user.email);
    if (!updatedUser) console.warn('Failed to update prompt count for:', req.user.email);

    res.json({ text: data.text });
  } catch (error) {
    console.error('OCR error:', error);
    res.status(500).json({ error: 'OCR processing failed' });
  } finally {
    if (filePath) fs.unlink(filePath, err => { if (err) console.error('Failed to delete file:', err); });
  }
});

// Analyze Endpoint
app.post('/analyze', verifyUserAndPromptLimit, async (req, res) => {
  try {
    const { text, conversation, model, language } = req.body;
    if (!text) return res.status(400).json({ error: 'No text provided for analysis' });

    console.log('Received conversation for /analyze:', conversation);
    console.log('Model:', model, 'Language:', language);

    const prompt = `Analyze this text from a coding question screenshot and provide a solution with code in ${language || 'any language'} and explanation:\n\n${text}\n\nFormat:\n\`\`\`\n[Code]\n\`\`\`\nExplanation: [Explanation]`;
    const aiResponse = await getAIExplanation(prompt, conversation, model, language);

    const updatedUser = await incrementPromptCount(req.user.email);
    if (!updatedUser) console.warn('Failed to update prompt count for:', req.user.email);

    res.json({
      response: aiResponse,
      user: { promptsUsed: updatedUser ? updatedUser.promptsUsed : req.user.promptsUsed, remainingTries: updatedUser ? updatedUser.remainingTries : req.user.remainingTries, subscriptionType: updatedUser ? updatedUser.subscriptionType : req.user.subscriptionType, maxPrompts: req.maxPrompts }
    });
  } catch (error) {
    console.error('AI analysis error:', error);
    res.status(500).json({ error: 'AI analysis failed', details: error.message });
  }
});

// Audio Processing Endpoint
app.post('/audio', upload.single('audio'), verifyUserAndPromptLimit, async (req, res) => {
  try {
    const { conversation, model, language } = req.body;
    if (!req.file) return res.status(400).json({ error: 'No audio file provided' });

    console.log('Received conversation for /audio:', conversation);
    console.log('Model:', model, 'Language:', language);

    const audioFilePath = req.file.path;
    const audioFile = fs.createReadStream(audioFilePath);
    const transcription = await openai.audio.transcriptions.create({ file: audioFile, model: 'whisper-1' });
    const transcribedText = transcription.text;

    const prompt = `Transcribed audio: "${transcribedText}"\n\nExplain what is being said in detail.`;
    const aiResponse = await getAIExplanation(prompt, conversation, model, language);

    const updatedUser = await incrementPromptCount(req.user.email);
    if (!updatedUser) console.warn('Failed to update prompt count for:', req.user.email);

    fs.unlink(audioFilePath, err => { if (err) console.error('Failed to delete audio file:', err); });

    res.json({
      transcription: transcribedText,
      analysis: aiResponse,
      user: { promptsUsed: updatedUser ? updatedUser.promptsUsed : req.user.promptsUsed, remainingTries: updatedUser ? updatedUser.remainingTries : req.user.remainingTries, subscriptionType: updatedUser ? updatedUser.subscriptionType : req.user.subscriptionType, maxPrompts: req.maxPrompts }
    });
  } catch (error) {
    console.error('Audio processing error:', error);
    res.status(500).json({ error: 'Failed to process audio', details: error.message });
  }
});

// Transcription Endpoint
app.post('/transcribe', verifyUserAndPromptLimit, async (req, res) => {
  try {
    const { audioChunk, email, conversation, model, language } = req.body;
    if (!audioChunk) return res.status(400).json({ error: 'No audio chunk provided' });

    console.log('Received conversation for /transcribe:', conversation);
    console.log('Model:', model, 'Language:', language);

    const audioBuffer = Buffer.from(audioChunk, 'base64');
    const tempDir = path.join(__dirname, 'temp');
    if (!fs.existsSync(tempDir)) fs.mkdirSync(tempDir, { recursive: true });
    const tempFilePath = path.join(tempDir, `audio-${Date.now()}.wav`);
    fs.writeFileSync(tempFilePath, audioBuffer);

    const audioFile = fs.createReadStream(tempFilePath);
    const transcription = await openai.audio.transcriptions.create({ file: audioFile, model: 'whisper-1', response_format: 'text' });
    const transcribedText = transcription.trim();
    console.log('Transcription:', transcribedText);

    let explanation = '';
    const wordCount = transcribedText.split(/\s+/).filter(word => word.length > 0).length;
    if (wordCount >= 10) {
      const prompt = `Transcribed audio: "${transcribedText}"\n\nExplain what is being said in detail.`;
      explanation = await getAIExplanation(prompt, conversation, model, language);
    } else {
      console.log(`Transcription has ${wordCount} words (< 10), skipping AI explanation`);
    }

    logTranscription(transcribedText, explanation);

    const updatedUser = await incrementPromptCount(email);
    if (!updatedUser) console.warn('Failed to update prompt count for:', email);

    fs.unlink(tempFilePath, err => { if (err) console.error('Failed to delete temporary audio file:', err); });

    res.json({
      text: transcribedText,
      explanation,
      user: { promptsUsed: updatedUser ? updatedUser.promptsUsed : req.user.promptsUsed, remainingTries: updatedUser ? updatedUser.remainingTries : req.user.remainingTries, subscriptionType: updatedUser ? updatedUser.subscriptionType : req.user.subscriptionType, maxPrompts: req.maxPrompts }
    });
  } catch (error) {
    console.error('Transcription error:', error);
    res.status(500).json({ error: 'Failed to transcribe audio chunk', details: error.message });
  }
});

// Chat Endpoint
app.post('/chat', verifyUserAndPromptLimit, async (req, res) => {
  try {
    const { prompt, conversation, model, language } = req.body;
    if (!prompt) return res.status(400).json({ error: 'No prompt provided' });

    console.log('Received conversation for /chat:', conversation);
    console.log('Model:', model, 'Language:', language);

    const aiResponse = await getAIExplanation(prompt, conversation, model, language);

    const updatedUser = await incrementPromptCount(req.user.email);
    if (!updatedUser) console.warn('Failed to update prompt count for:', req.user.email);

    res.json({
      response: aiResponse,
      user: { promptsUsed: updatedUser ? updatedUser.promptsUsed : req.user.promptsUsed, remainingTries: updatedUser ? updatedUser.remainingTries : req.user.remainingTries, subscriptionType: updatedUser ? updatedUser.subscriptionType : req.user.subscriptionType, maxPrompts: req.maxPrompts }
    });
  } catch (error) {
    console.error('Chat error:', error);
    res.status(500).json({ error: 'Chat processing failed', details: error.message });
  }
});

// User Stats Endpoint
app.get('/user-stats/:email', [
  param('email').isEmail().normalizeEmail({ gmail_remove_dots: false, all_lowercase: true }).withMessage('Valid email is required'),
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) return res.status(400).json({ error: errors.array()[0].msg });

  try {
    const { email } = req.params;
    const user = await User.findOne({ email }, { name: 1, email: 1, subscriptionType: 1, subscriptionEndDate: 1, remainingTries: 1, promptsUsed: 1, lastResetDate: 1, _id: 0 }).maxTimeMS(60000);
    if (!user) return res.status(404).json({ error: 'User not found' });

    res.json({ user });
  } catch (error) {
    console.error('User stats error:', error);
    res.status(500).json({ error: 'Failed to get user stats' });
  }
});

// Healthcheck Endpoint
app.get('/healthcheck', async (req, res) => {
  try {
    if (mongoose.connection.readyState === 1) {
      await mongoose.connection.db.admin().ping();
      return res.status(200).json({ status: 'healthy', connection: 'connected' });
    }
    return res.status(503).json({ status: 'unhealthy', connection: 'disconnected', readyState: mongoose.connection.readyState });
  } catch (error) {
    console.error('Healthcheck error:', error);
    return res.status(503).json({ status: 'unhealthy', error: error.message, readyState: mongoose.connection.readyState });
  }
});

// Check Update Endpoint
const LATEST_VERSION = process.env.LATEST_VERSION || '1.0.0';
app.get('/check-update', (req, res) => {
  const clientVersion = req.query.version;
  if (!clientVersion) return res.status(400).json({ error: 'Client version is required' });

  const isUpdateAvailable = compareVersions(clientVersion, LATEST_VERSION) < 0;
  res.json({
    isUpdateAvailable,
    clientVersion,
    latestVersion: LATEST_VERSION,
    message: isUpdateAvailable ? 'An update is available. Please download the latest version.' : 'You are on the latest version. No update required.'
  });
});

function compareVersions(v1, v2) {
  const v1Parts = v1.split('.').map(Number);
  const v2Parts = v2.split('.').map(Number);
  for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
    const part1 = v1Parts[i] || 0;
    const part2 = v2Parts[i] || 0;
    if (part1 < part2) return -1;
    if (part1 > part2) return 1;
  }
  return 0;
}

// Subscription and Prompt Management
async function updateSubscriptionStatus(user) {
  const now = new Date();
  if (user.subscriptionEndDate && user.subscriptionEndDate > now) {
    if (user.subscriptionType !== 'premium') {
      user.subscriptionType = 'premium';
      await user.save({ timeout: 30000 });
    }
  } else if (user.subscriptionType !== 'free') {
    user.subscriptionType = 'free';
    await user.save({ timeout: 30000 });
  }
  return user;
}

async function resetPromptsIfNeeded(user) {
  const now = new Date();
  if (user.subscriptionType === 'premium' && user.subscriptionEndDate && user.subscriptionEndDate > now) {
    if (!user.lastResetDate) {
      user.lastResetDate = now;
      await user.save({ timeout: 30000 });
    }
    const lastReset = new Date(user.lastResetDate);
    const oneWeek = 7 * 24 * 60 * 60 * 1000;
    if (now - lastReset >= oneWeek) {
      user.promptsUsed = 0;
      user.lastResetDate = now;
      await user.save({ timeout: 30000 });
    }
  }
  return user;
}

async function incrementPromptCount(email) {
  let retries = 0;
  const maxRetries = 3;
  while (retries < maxRetries) {
    try {
      const user = await User.findOneAndUpdate({ email }, { $inc: { promptsUsed: 1 } }, { new: true, maxTimeMS: 30000 });
      return user;
    } catch (error) {
      console.error(`Error incrementing prompt count (attempt ${retries + 1}/${maxRetries}):`, error);
      retries++;
      if (retries < maxRetries) await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, retries - 1)));
    }
  }
  console.error(`Failed to increment prompt count after ${maxRetries} attempts`);
  return null;
}

// Socket.IO for Realtime Audio Transcription
const server = http.createServer(app);
const io = new Server(server, { cors: { origin: "*" } });

io.on('connection', (socket) => {
  console.log(`Socket connected: ${socket.id}`);

  socket.on('audio_chunk', async (data) => {
    try {
      const { chunk, conversation, model, language } = data;
      if (!chunk) return socket.emit('error', { error: 'No audio chunk provided' });

      console.log(`Received conversation for socket ${socket.id}:`, conversation);
      console.log('Model:', model, 'Language:', language);

      const chunkBuffer = Buffer.from(chunk, 'base64');
      const tempDir = path.join(__dirname, 'temp');
      if (!fs.existsSync(tempDir)) fs.mkdirSync(tempDir, { recursive: true });
      const tempFilePath = path.join(tempDir, `socket-audio-${Date.now()}.wav`);
      fs.writeFileSync(tempFilePath, chunkBuffer);

      console.log(`Processing realtime audio for socket ${socket.id}`);
      const audioFile = fs.createReadStream(tempFilePath);
      const transcriptionResp = await openai.audio.transcriptions.create({ file: audioFile, model: 'whisper-1', response_format: 'text' });
      const transcribedText = transcriptionResp.trim();
      console.log(`Realtime transcription for ${socket.id}: ${transcribedText}`);

      let responseText = '';
      const wordCount = transcribedText.split(/\s+/).filter(word => word.length > 0).length;
      if (wordCount >= 10) {
        const prompt = `Transcribed audio: "${transcribedText}"\n\nExplain what is being said in detail.`;
        responseText = await getAIExplanation(prompt, conversation, model, language);
      } else {
        console.log(`Transcription < 10 words (${wordCount}), no AI response`);
      }

      logTranscription(transcribedText, responseText);
      socket.emit('transcription_result', { transcription: transcribedText, response: responseText });

      fs.unlink(tempFilePath, err => { if (err) console.error('Error deleting temporary audio file:', err); });
    } catch (err) {
      console.error(`Error processing audio chunk for socket ${socket.id}:`, err);
      socket.emit('error', { error: 'Realtime transcription failed', details: err.message });
    }
  });

  socket.on('disconnect', () => console.log(`Socket disconnected: ${socket.id}`));
});

// Graceful Shutdown
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully');
  for (const worker of workerPool) {
    try {
      await worker.terminate();
      console.log('Worker terminated successfully');
    } catch (error) {
      console.error('Error terminating worker:', error);
    }
  }
  try {
    await mongoose.connection.close();
    console.log('MongoDB connection closed');
  } catch (error) {
    console.error('Error closing MongoDB connection:', error);
  }
  process.exit(0);
});

process.on('uncaughtException', (error) => console.error('Uncaught Exception:', error));

server.listen(port, () => console.log(`Server running at http://localhost:${port}`));