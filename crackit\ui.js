// ui.js

// Ensure window.ui is not overwritten
if (!window.ui) {
  window.ui = {};
}

// Global variables
const isMac = navigator.platform.toLowerCase().includes('mac');

// Utility functions
function uiLog(message) {
  console.log(`[UI] ${message}`);
}

function escapeHTML(str) {
  return str.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#39;");
}

function parseMarkdown(text) {
  if (typeof marked === 'undefined') {
    uiLog("Warning: marked library not loaded, falling back to plain text");
    return escapeHTML(text);
  }
  // Customize marked to ensure code blocks with "mermaid" language are properly tagged
  marked.setOptions({
    highlight: function (code, lang) {
      if (lang === 'mermaid') {
        return code; // Return raw code for Mermaid, we'll handle it later
      }
      return hljs && lang ? hljs.highlight(code, { language: lang }).value : code;
    },
    langPrefix: 'language-'
  });
  // Use a renderer to customize the output for Mermaid code blocks
  const renderer = new marked.Renderer();
  renderer.code = function (code, lang) {
    if (lang === 'mermaid') {
      return `<pre class="mermaid">${code}</pre>`;
    }
    return `<pre><code class="language-${lang}">${code}</code></pre>`;
  };
  let html = marked.parse(text, { breaks: true, gfm: true, renderer });
  return `<div class="markdown-content">${html}</div>`;
}

// Function to render Mermaid diagrams using mermaid.run()
function renderMermaidDiagrams(container) {
  if (typeof mermaid === 'undefined') {
    uiLog("Error: Mermaid library not loaded");
    return;
  }
  // Look for both <pre class="mermaid"> and <pre> containing <code class="language-mermaid">
  const mermaidElements = container.querySelectorAll('pre.mermaid, pre.language-mermaid, pre:has(code.language-mermaid)');
  if (mermaidElements.length === 0) {
    uiLog("No Mermaid diagrams found in container");
    return;
  }
  mermaidElements.forEach((element, index) => {
    // If the element contains a <code> tag, get the code from there
    const codeElement = element.querySelector('code.language-mermaid') || element;
    const code = codeElement.textContent.trim();
    uiLog(`Raw Mermaid code: ${code}`);
    // Create a div to hold the rendered diagram
    const mermaidContainer = document.createElement('div');
    mermaidContainer.classList.add('mermaid');
    const uniqueId = `mermaid-diagram-${index}-${Date.now()}`;
    mermaidContainer.id = uniqueId;
    // Set the Mermaid code as the inner text of the div
    mermaidContainer.textContent = code;
    element.replaceWith(mermaidContainer);
    try {
      uiLog(`Rendering Mermaid diagram with ID: ${uniqueId}`);
      // Use mermaid.run() to render the diagram
      mermaid.run({
        nodes: [mermaidContainer],
        suppressErrors: true // Suppress errors to prevent breaking
      }).then(() => {
        uiLog(`Mermaid diagram rendered successfully: ${uniqueId}`);
        // Ensure the container is visible
        mermaidContainer.style.display = 'block';
      }).catch(error => {
        uiLog(`Error rendering Mermaid diagram: ${error.message}`);
        mermaidContainer.innerHTML = `<p>Error rendering diagram: ${error.message}</p>`;
      });
    } catch (error) {
      uiLog(`Error rendering Mermaid diagram: ${error.message}`);
      mermaidContainer.innerHTML = `<p>Error rendering diagram: ${error.message}</p>`;
    }
  });
}

// UI-related functions
function updateSizeDisplay(width, height) {
  const sizeElement = document.getElementById('window-size');
  if (sizeElement) {
    sizeElement.textContent = `${width} × ${height}`;
  } else {
    uiLog("Error: #window-size element not found");
  }
}

function setupTabNavigation() {
  const tabs = document.querySelectorAll('.tab');
  if (!tabs.length) {
    uiLog("Error: No .tab elements found");
    return;
  }
  tabs.forEach(tab => {
    tab.addEventListener('click', function() {
      document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
      document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
      this.classList.add('active');
      const tabId = this.getAttribute('data-tab');
      const content = document.getElementById(`${tabId}-tab`);
      if (content) {
        content.classList.add('active');
        uiLog(`Switched to tab: ${tabId}`);
      } else {
        uiLog(`Error: #${tabId}-tab not found`);
      }
    });
  });
}

function addChatMessage(containerId, message, isUser = false) {
  const chatMessages = document.getElementById(containerId);
  if (!chatMessages) {
    uiLog(`Error: #${containerId} not found`);
    return;
  }
  const messageDiv = document.createElement('div');
  messageDiv.classList.add('message', isUser ? 'user-message' : 'ai-message');
  messageDiv.innerHTML = parseMarkdown(message);
  const timeDiv = document.createElement('div');
  timeDiv.classList.add('message-time');
  timeDiv.textContent = new Date().toLocaleTimeString();
  messageDiv.appendChild(timeDiv);
  chatMessages.appendChild(messageDiv);
  setTimeout(() => {
    messageDiv.classList.add('visible');
    if (!isUser) {
      uiLog(`Rendering Mermaid diagrams for message in ${containerId}`);
      renderMermaidDiagrams(messageDiv);
      if (typeof hljs !== 'undefined') {
        messageDiv.querySelectorAll('pre code').forEach(block => hljs.highlightElement(block));
      }
    }
  }, 300); // Increased to 300ms to ensure DOM is ready
  chatMessages.scrollTop = chatMessages.scrollHeight;
  uiLog(`Added ${isUser ? 'user' : 'AI'} message to ${containerId}: ${message}`);
}

function addChatImage(containerId, imageDataUrl, isUser = false) {
  const chatMessages = document.getElementById(containerId);
  if (!chatMessages) {
    uiLog(`Error: #${containerId} not found`);
    return;
  }
  const messageDiv = document.createElement('div');
  messageDiv.classList.add('message', isUser ? 'user-message' : 'ai-message');
  
  const img = document.createElement('img');
  img.src = imageDataUrl;
  img.classList.add('message-image');
  messageDiv.appendChild(img);

  const timeDiv = document.createElement('div');
  timeDiv.classList.add('message-time');
  timeDiv.textContent = new Date().toLocaleTimeString();
  messageDiv.appendChild(timeDiv);

  chatMessages.appendChild(messageDiv);
  setTimeout(() => messageDiv.classList.add('visible'), 10);
  chatMessages.scrollTop = chatMessages.scrollHeight;
  uiLog(`Added ${isUser ? 'user' : 'AI'} image message to ${containerId}`);
}

function clearChat(containerId) {
  const chatMessages = document.getElementById(containerId);
  if (chatMessages) {
    chatMessages.innerHTML = '';
    const statusElement = document.getElementById(containerId === 'chat-messages' ? 'chat-status' : 'system-design-status');
    if (statusElement) statusElement.textContent = 'Ready';
    uiLog(`Chat cleared for ${containerId}`);
  } else {
    uiLog(`Error: #${containerId} not found for clearChat`);
  }
}

function populateShortcutsHeader() {
  const shortcutsHeader = document.getElementById('shortcuts-header');
  if (!shortcutsHeader) {
    uiLog("Error: #shortcuts-header not found");
    return;
  }
  const modifier = isMac ? 'Cmd' : 'Ctrl';
  const shortcutsHTML = `
    <div>
      <span class="shortcut-key">${modifier}+Shift+C</span> Capture screen • 
      <span class="shortcut-key">${modifier}+Shift+P</span> Process capture • 
      <span class="shortcut-key">${modifier}+Shift+R</span> Start/Stop recording
    </div>
    <div>
      <span class="shortcut-key">${modifier}+↑↓←→</span> Move • 
      <span class="shortcut-key">${modifier}+Shift+↑↓</span> Resize • 
      <span class="shortcut-key">${modifier}+/-</span> Zoom
    </div>
    <div>
      <span class="shortcut-key">${modifier}+Shift+T</span> Toggle window • 
      <span class="shortcut-key">${modifier}+Shift+K</span> Decrease height • 
      <span class="shortcut-key">${modifier}+Alt+↑↓</span> Scroll active tab
    </div>
  `;
  shortcutsHeader.innerHTML = shortcutsHTML;
  uiLog("Shortcuts header populated");
}

function populateShortcutsHelp() {
  const shortcutsElement = document.getElementById('shortcuts-tab');
  if (!shortcutsElement) {
    uiLog("Error: #shortcuts-tab not found");
    return;
  }
  const modifier = isMac ? 'Cmd' : 'Ctrl';
  const shortcuts = [
    { keys: `${modifier}+Shift+C`, description: 'Capture screen' },
    { keys: `${modifier}+Shift+P`, description: 'Process captured image' },
    { keys: `${modifier}+Shift+R`, description: 'Start/Stop recording' },
    { keys: `${modifier}+Shift+T`, description: 'Toggle window visibility' },
    { keys: `${modifier}+Arrow Keys`, description: 'Move window' },
    { keys: `${modifier}+Shift+Arrow Left/Right`, description: 'Resize window width' },
    { keys: `${modifier}+Shift+Arrow Up/Down`, description: 'Resize window height' },
    { keys: `${modifier}+Shift+I`, description: 'Increase window height' },
    { keys: `${modifier}+Shift+K`, description: 'Decrease window height' },
    { keys: `${modifier}+Plus`, description: 'Increase window size' },
    { keys: `${modifier}+Minus`, description: 'Decrease window size' },
    { keys: `${modifier}+Alt+Up`, description: 'Scroll up active tab' },
    { keys: `${modifier}+Alt+Down`, description: 'Scroll down active tab' },
  ];
  let shortcutsHTML = '<div class="shortcuts-container"><h2>Keyboard Shortcuts</h2><table class="shortcuts-table">';
  shortcutsHTML += '<tr><th>Shortcut</th><th>Action</th></tr>';
  shortcuts.forEach(shortcut => {
    shortcutsHTML += `<tr><td class="shortcut-key">${shortcut.keys}</td><td>${shortcut.description}</td></tr>`;
  });
  shortcutsHTML += '</table></div>';
  shortcutsElement.innerHTML = shortcutsHTML;
  uiLog("Shortcuts help populated");
}

// Initialize UI functions after DOM is fully loaded
document.addEventListener('DOMContentLoaded', () => {
  uiLog("UI script loaded and DOM is ready");

  // Initialize Mermaid
  if (typeof mermaid !== 'undefined') {
    mermaid.initialize({
      startOnLoad: false,
      securityLevel: 'loose',
      theme: 'dark',
      logLevel: 'debug',
      flowchart: {
        useMaxWidth: true,
        htmlLabels: true,
        curve: 'basis'
      },
      themeCSS: `
        .node rect, .node circle, .node ellipse, .node polygon {
          fill: rgba(255, 255, 255, 0.1) !important;
          stroke: #d1d5db !important;
          stroke-width: 1px !important;
        }
        .node text {
          fill: #d1d5db !important;
        }
        .edgePath {
          stroke: #d1d5db !important;
          stroke-width: 1.5px !important;
        }
        .edgeLabel {
          fill: #d1d5db !important;
          background: rgba(0, 0, 0, 0.5) !important;
        }
      `
    });
    uiLog("Mermaid initialized with dark theme and custom styles");
  } else {
    uiLog("Error: Mermaid library not loaded");
  }

  // Populate UI elements
  populateShortcutsHeader();
  populateShortcutsHelp();
  setupTabNavigation();

  // Expose functions and variables to window.ui
  window.ui.isMac = isMac;
  window.ui.uiLog = uiLog;
  window.ui.updateSizeDisplay = updateSizeDisplay;
  window.ui.setupTabNavigation = setupTabNavigation;
  window.ui.addChatMessage = addChatMessage;
  window.ui.addChatImage = addChatImage;
  window.ui.clearChat = clearChat;
  window.ui.populateShortcutsHeader = populateShortcutsHeader;
  window.ui.populateShortcutsHelp = populateShortcutsHelp;

  uiLog("UI functions exposed to window.ui");
});